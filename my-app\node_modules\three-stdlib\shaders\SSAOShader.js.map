{"version": 3, "file": "SSAOShader.js", "sources": ["../../src/shaders/SSAOShader.ts"], "sourcesContent": ["import { Matrix4, Vector2 } from 'three'\n\n/**\n * References:\n * http://john-chapman-graphics.blogspot.com/2013/01/ssao-tutorial.html\n * https://learnopengl.com/Advanced-Lighting/SSAO\n * https://github.com/McNopper/OpenGL/blob/master/Example28/shader/ssao.frag.glsl\n */\n\nexport const SSAOShader = {\n  defines: {\n    PERSPECTIVE_CAMERA: 1,\n    KERNEL_SIZE: 32,\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    tNormal: { value: null },\n    tDepth: { value: null },\n    tNoise: { value: null },\n    kernel: { value: null },\n    cameraNear: { value: null },\n    cameraFar: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2() },\n    cameraProjectionMatrix: { value: /* @__PURE__ */ new Matrix4() },\n    cameraInverseProjectionMatrix: { value: /* @__PURE__ */ new Matrix4() },\n    kernelRadius: { value: 8 },\n    minDistance: { value: 0.005 },\n    maxDistance: { value: 0.05 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tNormal;\n    uniform sampler2D tDepth;\n    uniform sampler2D tNoise;\n\n    uniform vec3 kernel[ KERNEL_SIZE ];\n\n    uniform vec2 resolution;\n\n    uniform float cameraNear;\n    uniform float cameraFar;\n    uniform mat4 cameraProjectionMatrix;\n    uniform mat4 cameraInverseProjectionMatrix;\n\n    uniform float kernelRadius;\n    uniform float minDistance; // avoid artifacts caused by neighbour fragments with minimal depth difference\n    uniform float maxDistance; // avoid the influence of fragments which are too far away\n\n    varying vec2 vUv;\n\n    #include <packing>\n\n    float getDepth( const in vec2 screenPosition ) {\n\n    \treturn texture2D( tDepth, screenPosition ).x;\n\n    }\n\n    float getLinearDepth( const in vec2 screenPosition ) {\n\n    \t#if PERSPECTIVE_CAMERA == 1\n\n    \t\tfloat fragCoordZ = texture2D( tDepth, screenPosition ).x;\n    \t\tfloat viewZ = perspectiveDepthToViewZ( fragCoordZ, cameraNear, cameraFar );\n    \t\treturn viewZToOrthographicDepth( viewZ, cameraNear, cameraFar );\n\n    \t#else\n\n    \t\treturn texture2D( tDepth, screenPosition ).x;\n\n    \t#endif\n\n    }\n\n    float getViewZ( const in float depth ) {\n\n    \t#if PERSPECTIVE_CAMERA == 1\n\n    \t\treturn perspectiveDepthToViewZ( depth, cameraNear, cameraFar );\n\n    \t#else\n\n    \t\treturn orthographicDepthToViewZ( depth, cameraNear, cameraFar );\n\n    \t#endif\n\n    }\n\n    vec3 getViewPosition( const in vec2 screenPosition, const in float depth, const in float viewZ ) {\n\n    \tfloat clipW = cameraProjectionMatrix[2][3] * viewZ + cameraProjectionMatrix[3][3];\n\n    \tvec4 clipPosition = vec4( ( vec3( screenPosition, depth ) - 0.5 ) * 2.0, 1.0 );\n\n    \tclipPosition *= clipW; // unprojection.\n\n    \treturn ( cameraInverseProjectionMatrix * clipPosition ).xyz;\n\n    }\n\n    vec3 getViewNormal( const in vec2 screenPosition ) {\n\n    \treturn unpackRGBToNormal( texture2D( tNormal, screenPosition ).xyz );\n\n    }\n\n    void main() {\n\n    \tfloat depth = getDepth( vUv );\n    \tfloat viewZ = getViewZ( depth );\n\n    \tvec3 viewPosition = getViewPosition( vUv, depth, viewZ );\n    \tvec3 viewNormal = getViewNormal( vUv );\n\n     vec2 noiseScale = vec2( resolution.x / 4.0, resolution.y / 4.0 );\n    \tvec3 random = texture2D( tNoise, vUv * noiseScale ).xyz;\n\n    // compute matrix used to reorient a kernel vector\n\n    \tvec3 tangent = normalize( random - viewNormal * dot( random, viewNormal ) );\n    \tvec3 bitangent = cross( viewNormal, tangent );\n    \tmat3 kernelMatrix = mat3( tangent, bitangent, viewNormal );\n\n     float occlusion = 0.0;\n\n     for ( int i = 0; i < KERNEL_SIZE; i ++ ) {\n\n    \t\tvec3 sampleVector = kernelMatrix * kernel[ i ]; // reorient sample vector in view space\n    \t\tvec3 samplePoint = viewPosition + ( sampleVector * kernelRadius ); // calculate sample point\n\n    \t\tvec4 samplePointNDC = cameraProjectionMatrix * vec4( samplePoint, 1.0 ); // project point and calculate NDC\n    \t\tsamplePointNDC /= samplePointNDC.w;\n\n    \t\tvec2 samplePointUv = samplePointNDC.xy * 0.5 + 0.5; // compute uv coordinates\n\n    \t\tfloat realDepth = getLinearDepth( samplePointUv ); // get linear depth from depth texture\n    \t\tfloat sampleDepth = viewZToOrthographicDepth( samplePoint.z, cameraNear, cameraFar ); // compute linear depth of the sample view Z value\n    \t\tfloat delta = sampleDepth - realDepth;\n\n    \t\tif ( delta > minDistance && delta < maxDistance ) { // if fragment is before sample point, increase occlusion\n\n    \t\t\tocclusion += 1.0;\n\n    \t\t}\n\n    \t}\n\n    \tocclusion = clamp( occlusion / float( KERNEL_SIZE ), 0.0, 1.0 );\n\n    \tgl_FragColor = vec4( vec3( 1.0 - occlusion ), 1.0 );\n\n    }\n  `,\n}\n\nexport const SSAODepthShader = {\n  defines: {\n    PERSPECTIVE_CAMERA: 1,\n  },\n\n  uniforms: {\n    tDepth: { value: null },\n    cameraNear: { value: null },\n    cameraFar: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDepth;\n\n    uniform float cameraNear;\n    uniform float cameraFar;\n\n    varying vec2 vUv;\n\n    #include <packing>\n\n    float getLinearDepth( const in vec2 screenPosition ) {\n\n    \t#if PERSPECTIVE_CAMERA == 1\n\n    \t\tfloat fragCoordZ = texture2D( tDepth, screenPosition ).x;\n    \t\tfloat viewZ = perspectiveDepthToViewZ( fragCoordZ, cameraNear, cameraFar );\n    \t\treturn viewZToOrthographicDepth( viewZ, cameraNear, cameraFar );\n\n    \t#else\n\n    \t\treturn texture2D( tDepth, screenPosition ).x;\n\n    \t#endif\n\n    }\n\n    void main() {\n\n    \tfloat depth = getLinearDepth( vUv );\n    \tgl_FragColor = vec4( vec3( 1.0 - depth ), 1.0 );\n\n    }\n  `,\n}\n\nexport const SSAOBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2() },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 texelSize = ( 1.0 / resolution );\n    \tfloat result = 0.0;\n\n    \tfor ( int i = - 2; i <= 2; i ++ ) {\n\n    \t\tfor ( int j = - 2; j <= 2; j ++ ) {\n\n    \t\t\tvec2 offset = ( vec2( float( i ), float( j ) ) ) * texelSize;\n    \t\t\tresult += texture2D( tDiffuse, vUv + offset ).r;\n\n    \t\t}\n\n    \t}\n\n    \tgl_FragColor = vec4( vec3( result / ( 5.0 * 5.0 ) ), 1.0 );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AASO,MAAM,aAAa;AAAA,EACxB,SAAS;AAAA,IACP,oBAAoB;AAAA,IACpB,aAAa;AAAA,EACf;AAAA,EAEA,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,KAAK;AAAA,IACzB,YAAY,EAAE,OAAuB,oBAAI,UAAU;AAAA,IACnD,wBAAwB,EAAE,OAAuB,oBAAI,UAAU;AAAA,IAC/D,+BAA+B,EAAE,OAAuB,oBAAI,UAAU;AAAA,IACtE,cAAc,EAAE,OAAO,EAAE;AAAA,IACzB,aAAa,EAAE,OAAO,KAAM;AAAA,IAC5B,aAAa,EAAE,OAAO,KAAK;AAAA,EAC7B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4H7B;AAEO,MAAM,kBAAkB;AAAA,EAC7B,SAAS;AAAA,IACP,oBAAoB;AAAA,EACtB;AAAA,EAEA,UAAU;AAAA,IACR,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,YAAY,EAAE,OAAO,KAAK;AAAA,IAC1B,WAAW,EAAE,OAAO,KAAK;AAAA,EAC3B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiC7B;AAEO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAuB,oBAAI,UAAU;AAAA,EACrD;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2B7B;"}