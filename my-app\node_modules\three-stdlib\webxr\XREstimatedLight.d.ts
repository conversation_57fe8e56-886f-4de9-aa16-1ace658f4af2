/// <reference types="webxr" />
import { DirectionalLight, Group, LightProbe, WebGLRenderer, Texture } from 'three'

export class SessionLightProbe {
  xrLight: XREstimatedLight
  renderer: WebGLRenderer
  lightProbe: unknown
  xrWebGLBinding: unknown | null
  estimationStartCallback: () => void
  frameCallback: (this: SessionLightProbe, time: number, xrFrame: XRFrame) => void

  constructor(
    xrLight: XREstimatedLight,
    renderer: WebGLRenderer,
    lightProbe: unknown,
    environmentEstimation: boolean,
    estimationStartCallback: () => void,
  )

  updateReflection: () => void

  onXRFrame: (time: number, xrFrame: XRFrame) => void

  dispose: () => void
}

export class XREstimatedLight extends Group {
  lightProbe: LightProbe
  directionalLight: DirectionalLight
  environment: Texture

  constructor(renderer: WebGLRenderer, environmentEstimation?: boolean)
}
