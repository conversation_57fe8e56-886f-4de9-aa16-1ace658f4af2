{"version": 3, "file": "SelectionBox.cjs", "sources": ["../../src/interactive/SelectionBox.js"], "sourcesContent": ["import { Frustum, Vector3 } from 'three'\n\nconst frustum = /* @__PURE__ */ new Frustum()\nconst center = /* @__PURE__ */ new Vector3()\n\nconst tmpPoint = /* @__PURE__ */ new Vector3()\n\nconst vecNear = /* @__PURE__ */ new Vector3()\nconst vecTopLeft = /* @__PURE__ */ new Vector3()\nconst vecTopRight = /* @__PURE__ */ new Vector3()\nconst vecDownRight = /* @__PURE__ */ new Vector3()\nconst vecDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vecFarTopLeft = /* @__PURE__ */ new Vector3()\nconst vecFarTopRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vectemp1 = /* @__PURE__ */ new Vector3()\nconst vectemp2 = /* @__PURE__ */ new Vector3()\nconst vectemp3 = /* @__PURE__ */ new Vector3()\n\nclass SelectionBox {\n  constructor(camera, scene, deep) {\n    this.camera = camera\n    this.scene = scene\n    this.startPoint = new Vector3()\n    this.endPoint = new Vector3()\n    this.collection = []\n    this.deep = deep || Number.MAX_VALUE\n  }\n\n  select(startPoint, endPoint) {\n    this.startPoint = startPoint || this.startPoint\n    this.endPoint = endPoint || this.endPoint\n    this.collection = []\n\n    this.updateFrustum(this.startPoint, this.endPoint)\n    this.searchChildInFrustum(frustum, this.scene)\n\n    return this.collection\n  }\n\n  updateFrustum(startPoint, endPoint) {\n    startPoint = startPoint || this.startPoint\n    endPoint = endPoint || this.endPoint\n\n    // Avoid invalid frustum\n\n    if (startPoint.x === endPoint.x) {\n      endPoint.x += Number.EPSILON\n    }\n\n    if (startPoint.y === endPoint.y) {\n      endPoint.y += Number.EPSILON\n    }\n\n    this.camera.updateProjectionMatrix()\n    this.camera.updateMatrixWorld()\n\n    if (this.camera.isPerspectiveCamera) {\n      tmpPoint.copy(startPoint)\n      tmpPoint.x = Math.min(startPoint.x, endPoint.x)\n      tmpPoint.y = Math.max(startPoint.y, endPoint.y)\n      endPoint.x = Math.max(startPoint.x, endPoint.x)\n      endPoint.y = Math.min(startPoint.y, endPoint.y)\n\n      vecNear.setFromMatrixPosition(this.camera.matrixWorld)\n      vecTopLeft.copy(tmpPoint)\n      vecTopRight.set(endPoint.x, tmpPoint.y, 0)\n      vecDownRight.copy(endPoint)\n      vecDownLeft.set(tmpPoint.x, endPoint.y, 0)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vectemp1.copy(vecTopLeft).sub(vecNear)\n      vectemp2.copy(vecTopRight).sub(vecNear)\n      vectemp3.copy(vecDownRight).sub(vecNear)\n      vectemp1.normalize()\n      vectemp2.normalize()\n      vectemp3.normalize()\n\n      vectemp1.multiplyScalar(this.deep)\n      vectemp2.multiplyScalar(this.deep)\n      vectemp3.multiplyScalar(this.deep)\n      vectemp1.add(vecNear)\n      vectemp2.add(vecNear)\n      vectemp3.add(vecNear)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecNear, vecTopLeft, vecTopRight)\n      planes[1].setFromCoplanarPoints(vecNear, vecTopRight, vecDownRight)\n      planes[2].setFromCoplanarPoints(vecDownRight, vecDownLeft, vecNear)\n      planes[3].setFromCoplanarPoints(vecDownLeft, vecTopLeft, vecNear)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vectemp3, vectemp2, vectemp1)\n      planes[5].normal.multiplyScalar(-1)\n    } else if (this.camera.isOrthographicCamera) {\n      const left = Math.min(startPoint.x, endPoint.x)\n      const top = Math.max(startPoint.y, endPoint.y)\n      const right = Math.max(startPoint.x, endPoint.x)\n      const down = Math.min(startPoint.y, endPoint.y)\n\n      vecTopLeft.set(left, top, -1)\n      vecTopRight.set(right, top, -1)\n      vecDownRight.set(right, down, -1)\n      vecDownLeft.set(left, down, -1)\n\n      vecFarTopLeft.set(left, top, 1)\n      vecFarTopRight.set(right, top, 1)\n      vecFarDownRight.set(right, down, 1)\n      vecFarDownLeft.set(left, down, 1)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vecFarTopLeft.unproject(this.camera)\n      vecFarTopRight.unproject(this.camera)\n      vecFarDownRight.unproject(this.camera)\n      vecFarDownLeft.unproject(this.camera)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecTopLeft, vecFarTopLeft, vecFarTopRight)\n      planes[1].setFromCoplanarPoints(vecTopRight, vecFarTopRight, vecFarDownRight)\n      planes[2].setFromCoplanarPoints(vecFarDownRight, vecFarDownLeft, vecDownLeft)\n      planes[3].setFromCoplanarPoints(vecFarDownLeft, vecFarTopLeft, vecTopLeft)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vecFarDownRight, vecFarTopRight, vecFarTopLeft)\n      planes[5].normal.multiplyScalar(-1)\n    } else {\n      console.error('THREE.SelectionBox: Unsupported camera type.')\n    }\n  }\n\n  searchChildInFrustum(frustum, object) {\n    if (object.isMesh || object.isLine || object.isPoints) {\n      if (object.material !== undefined) {\n        if (object.geometry.boundingSphere === null) object.geometry.computeBoundingSphere()\n\n        center.copy(object.geometry.boundingSphere.center)\n\n        center.applyMatrix4(object.matrixWorld)\n\n        if (frustum.containsPoint(center)) {\n          this.collection.push(object)\n        }\n      }\n    }\n\n    if (object.children.length > 0) {\n      for (let x = 0; x < object.children.length; x++) {\n        this.searchChildInFrustum(frustum, object.children[x])\n      }\n    }\n  }\n}\n\nexport { SelectionBox }\n"], "names": ["Frustum", "Vector3", "frustum"], "mappings": ";;;AAEA,MAAM,UAA0B,oBAAIA,MAAAA,QAAS;AAC7C,MAAM,SAAyB,oBAAIC,MAAAA,QAAS;AAE5C,MAAM,WAA2B,oBAAIA,MAAAA,QAAS;AAE9C,MAAM,UAA0B,oBAAIA,MAAAA,QAAS;AAC7C,MAAM,aAA6B,oBAAIA,MAAAA,QAAS;AAChD,MAAM,cAA8B,oBAAIA,MAAAA,QAAS;AACjD,MAAM,eAA+B,oBAAIA,MAAAA,QAAS;AAClD,MAAM,cAA8B,oBAAIA,MAAAA,QAAS;AAEjD,MAAM,gBAAgC,oBAAIA,MAAAA,QAAS;AACnD,MAAM,iBAAiC,oBAAIA,MAAAA,QAAS;AACpD,MAAM,kBAAkC,oBAAIA,MAAAA,QAAS;AACrD,MAAM,iBAAiC,oBAAIA,MAAAA,QAAS;AAEpD,MAAM,WAA2B,oBAAIA,MAAAA,QAAS;AAC9C,MAAM,WAA2B,oBAAIA,MAAAA,QAAS;AAC9C,MAAM,WAA2B,oBAAIA,MAAAA,QAAS;AAE9C,MAAM,aAAa;AAAA,EACjB,YAAY,QAAQ,OAAO,MAAM;AAC/B,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,aAAa,IAAIA,cAAS;AAC/B,SAAK,WAAW,IAAIA,cAAS;AAC7B,SAAK,aAAa,CAAE;AACpB,SAAK,OAAO,QAAQ,OAAO;AAAA,EAC5B;AAAA,EAED,OAAO,YAAY,UAAU;AAC3B,SAAK,aAAa,cAAc,KAAK;AACrC,SAAK,WAAW,YAAY,KAAK;AACjC,SAAK,aAAa,CAAE;AAEpB,SAAK,cAAc,KAAK,YAAY,KAAK,QAAQ;AACjD,SAAK,qBAAqB,SAAS,KAAK,KAAK;AAE7C,WAAO,KAAK;AAAA,EACb;AAAA,EAED,cAAc,YAAY,UAAU;AAClC,iBAAa,cAAc,KAAK;AAChC,eAAW,YAAY,KAAK;AAI5B,QAAI,WAAW,MAAM,SAAS,GAAG;AAC/B,eAAS,KAAK,OAAO;AAAA,IACtB;AAED,QAAI,WAAW,MAAM,SAAS,GAAG;AAC/B,eAAS,KAAK,OAAO;AAAA,IACtB;AAED,SAAK,OAAO,uBAAwB;AACpC,SAAK,OAAO,kBAAmB;AAE/B,QAAI,KAAK,OAAO,qBAAqB;AACnC,eAAS,KAAK,UAAU;AACxB,eAAS,IAAI,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC9C,eAAS,IAAI,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC9C,eAAS,IAAI,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC9C,eAAS,IAAI,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAE9C,cAAQ,sBAAsB,KAAK,OAAO,WAAW;AACrD,iBAAW,KAAK,QAAQ;AACxB,kBAAY,IAAI,SAAS,GAAG,SAAS,GAAG,CAAC;AACzC,mBAAa,KAAK,QAAQ;AAC1B,kBAAY,IAAI,SAAS,GAAG,SAAS,GAAG,CAAC;AAEzC,iBAAW,UAAU,KAAK,MAAM;AAChC,kBAAY,UAAU,KAAK,MAAM;AACjC,mBAAa,UAAU,KAAK,MAAM;AAClC,kBAAY,UAAU,KAAK,MAAM;AAEjC,eAAS,KAAK,UAAU,EAAE,IAAI,OAAO;AACrC,eAAS,KAAK,WAAW,EAAE,IAAI,OAAO;AACtC,eAAS,KAAK,YAAY,EAAE,IAAI,OAAO;AACvC,eAAS,UAAW;AACpB,eAAS,UAAW;AACpB,eAAS,UAAW;AAEpB,eAAS,eAAe,KAAK,IAAI;AACjC,eAAS,eAAe,KAAK,IAAI;AACjC,eAAS,eAAe,KAAK,IAAI;AACjC,eAAS,IAAI,OAAO;AACpB,eAAS,IAAI,OAAO;AACpB,eAAS,IAAI,OAAO;AAEpB,UAAI,SAAS,QAAQ;AAErB,aAAO,CAAC,EAAE,sBAAsB,SAAS,YAAY,WAAW;AAChE,aAAO,CAAC,EAAE,sBAAsB,SAAS,aAAa,YAAY;AAClE,aAAO,CAAC,EAAE,sBAAsB,cAAc,aAAa,OAAO;AAClE,aAAO,CAAC,EAAE,sBAAsB,aAAa,YAAY,OAAO;AAChE,aAAO,CAAC,EAAE,sBAAsB,aAAa,cAAc,WAAW;AACtE,aAAO,CAAC,EAAE,sBAAsB,UAAU,UAAU,QAAQ;AAC5D,aAAO,CAAC,EAAE,OAAO,eAAe,EAAE;AAAA,IACxC,WAAe,KAAK,OAAO,sBAAsB;AAC3C,YAAM,OAAO,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC9C,YAAM,MAAM,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC7C,YAAM,QAAQ,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAC/C,YAAM,OAAO,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC;AAE9C,iBAAW,IAAI,MAAM,KAAK,EAAE;AAC5B,kBAAY,IAAI,OAAO,KAAK,EAAE;AAC9B,mBAAa,IAAI,OAAO,MAAM,EAAE;AAChC,kBAAY,IAAI,MAAM,MAAM,EAAE;AAE9B,oBAAc,IAAI,MAAM,KAAK,CAAC;AAC9B,qBAAe,IAAI,OAAO,KAAK,CAAC;AAChC,sBAAgB,IAAI,OAAO,MAAM,CAAC;AAClC,qBAAe,IAAI,MAAM,MAAM,CAAC;AAEhC,iBAAW,UAAU,KAAK,MAAM;AAChC,kBAAY,UAAU,KAAK,MAAM;AACjC,mBAAa,UAAU,KAAK,MAAM;AAClC,kBAAY,UAAU,KAAK,MAAM;AAEjC,oBAAc,UAAU,KAAK,MAAM;AACnC,qBAAe,UAAU,KAAK,MAAM;AACpC,sBAAgB,UAAU,KAAK,MAAM;AACrC,qBAAe,UAAU,KAAK,MAAM;AAEpC,UAAI,SAAS,QAAQ;AAErB,aAAO,CAAC,EAAE,sBAAsB,YAAY,eAAe,cAAc;AACzE,aAAO,CAAC,EAAE,sBAAsB,aAAa,gBAAgB,eAAe;AAC5E,aAAO,CAAC,EAAE,sBAAsB,iBAAiB,gBAAgB,WAAW;AAC5E,aAAO,CAAC,EAAE,sBAAsB,gBAAgB,eAAe,UAAU;AACzE,aAAO,CAAC,EAAE,sBAAsB,aAAa,cAAc,WAAW;AACtE,aAAO,CAAC,EAAE,sBAAsB,iBAAiB,gBAAgB,aAAa;AAC9E,aAAO,CAAC,EAAE,OAAO,eAAe,EAAE;AAAA,IACxC,OAAW;AACL,cAAQ,MAAM,8CAA8C;AAAA,IAC7D;AAAA,EACF;AAAA,EAED,qBAAqBC,UAAS,QAAQ;AACpC,QAAI,OAAO,UAAU,OAAO,UAAU,OAAO,UAAU;AACrD,UAAI,OAAO,aAAa,QAAW;AACjC,YAAI,OAAO,SAAS,mBAAmB;AAAM,iBAAO,SAAS,sBAAuB;AAEpF,eAAO,KAAK,OAAO,SAAS,eAAe,MAAM;AAEjD,eAAO,aAAa,OAAO,WAAW;AAEtC,YAAIA,SAAQ,cAAc,MAAM,GAAG;AACjC,eAAK,WAAW,KAAK,MAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAED,QAAI,OAAO,SAAS,SAAS,GAAG;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAK;AAC/C,aAAK,qBAAqBA,UAAS,OAAO,SAAS,CAAC,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AACH;;"}