{"version": 3, "file": "PositionalAudioHelper.js", "sources": ["../../src/helpers/PositionalAudioHelper.js"], "sourcesContent": ["import { BufferGeo<PERSON>, BufferAttribute, LineBasicMaterial, Line, MathUtils } from 'three'\n\nclass PositionalAudioHelper extends Line {\n  constructor(audio, range = 1, divisionsInnerAngle = 16, divisionsOuterAngle = 2) {\n    const geometry = new BufferGeometry()\n    const divisions = divisionsInnerAngle + divisionsOuterAngle * 2\n    const positions = new Float32Array((divisions * 3 + 3) * 3)\n    geometry.setAttribute('position', new BufferAttribute(positions, 3))\n\n    const materialInnerAngle = new LineBasicMaterial({ color: 0x00ff00 })\n    const materialOuterAngle = new LineBasicMaterial({ color: 0xffff00 })\n\n    super(geometry, [materialOuterAngle, materialInnerAngle])\n\n    this.type = 'PositionalAudioHelper'\n    this.audio = audio\n    this.range = range\n    this.divisionsInnerAngle = divisionsInnerAngle\n    this.divisionsOuterAngle = divisionsOuterAngle\n\n    this.update()\n  }\n\n  update() {\n    const audio = this.audio\n    const range = this.range\n    const divisionsInnerAngle = this.divisionsInnerAngle\n    const divisionsOuterAngle = this.divisionsOuterAngle\n\n    const coneInnerAngle = MathUtils.degToRad(audio.panner.coneInnerAngle)\n    const coneOuterAngle = MathUtils.degToRad(audio.panner.coneOuterAngle)\n\n    const halfConeInnerAngle = coneInnerAngle / 2\n    const halfConeOuterAngle = coneOuterAngle / 2\n\n    let start = 0\n    let count = 0\n    let i, stride\n\n    const geometry = this.geometry\n    const positionAttribute = geometry.attributes.position\n\n    geometry.clearGroups()\n\n    //\n\n    function generateSegment(from, to, divisions, materialIndex) {\n      const step = (to - from) / divisions\n\n      positionAttribute.setXYZ(start, 0, 0, 0)\n      count++\n\n      for (i = from; i < to; i += step) {\n        stride = start + count\n\n        positionAttribute.setXYZ(stride, Math.sin(i) * range, 0, Math.cos(i) * range)\n        positionAttribute.setXYZ(\n          stride + 1,\n          Math.sin(Math.min(i + step, to)) * range,\n          0,\n          Math.cos(Math.min(i + step, to)) * range,\n        )\n        positionAttribute.setXYZ(stride + 2, 0, 0, 0)\n\n        count += 3\n      }\n\n      geometry.addGroup(start, count, materialIndex)\n\n      start += count\n      count = 0\n    }\n\n    //\n\n    generateSegment(-halfConeOuterAngle, -halfConeInnerAngle, divisionsOuterAngle, 0)\n    generateSegment(-halfConeInnerAngle, halfConeInnerAngle, divisionsInnerAngle, 1)\n    generateSegment(halfConeInnerAngle, halfConeOuterAngle, divisionsOuterAngle, 0)\n\n    //\n\n    positionAttribute.needsUpdate = true\n\n    if (coneInnerAngle === coneOuterAngle) this.material[0].visible = false\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material[0].dispose()\n    this.material[1].dispose()\n  }\n}\n\nexport { PositionalAudioHelper }\n"], "names": [], "mappings": ";AAEA,MAAM,8BAA8B,KAAK;AAAA,EACvC,YAAY,OAAO,QAAQ,GAAG,sBAAsB,IAAI,sBAAsB,GAAG;AAC/E,UAAM,WAAW,IAAI,eAAgB;AACrC,UAAM,YAAY,sBAAsB,sBAAsB;AAC9D,UAAM,YAAY,IAAI,cAAc,YAAY,IAAI,KAAK,CAAC;AAC1D,aAAS,aAAa,YAAY,IAAI,gBAAgB,WAAW,CAAC,CAAC;AAEnE,UAAM,qBAAqB,IAAI,kBAAkB,EAAE,OAAO,MAAQ,CAAE;AACpE,UAAM,qBAAqB,IAAI,kBAAkB,EAAE,OAAO,SAAQ,CAAE;AAEpE,UAAM,UAAU,CAAC,oBAAoB,kBAAkB,CAAC;AAExD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;AAE3B,SAAK,OAAQ;AAAA,EACd;AAAA,EAED,SAAS;AACP,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,sBAAsB,KAAK;AACjC,UAAM,sBAAsB,KAAK;AAEjC,UAAM,iBAAiB,UAAU,SAAS,MAAM,OAAO,cAAc;AACrE,UAAM,iBAAiB,UAAU,SAAS,MAAM,OAAO,cAAc;AAErE,UAAM,qBAAqB,iBAAiB;AAC5C,UAAM,qBAAqB,iBAAiB;AAE5C,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,GAAG;AAEP,UAAM,WAAW,KAAK;AACtB,UAAM,oBAAoB,SAAS,WAAW;AAE9C,aAAS,YAAa;AAItB,aAAS,gBAAgB,MAAM,IAAI,WAAW,eAAe;AAC3D,YAAM,QAAQ,KAAK,QAAQ;AAE3B,wBAAkB,OAAO,OAAO,GAAG,GAAG,CAAC;AACvC;AAEA,WAAK,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM;AAChC,iBAAS,QAAQ;AAEjB,0BAAkB,OAAO,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,IAAI,KAAK;AAC5E,0BAAkB;AAAA,UAChB,SAAS;AAAA,UACT,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,UACnC;AAAA,UACA,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM,EAAE,CAAC,IAAI;AAAA,QACpC;AACD,0BAAkB,OAAO,SAAS,GAAG,GAAG,GAAG,CAAC;AAE5C,iBAAS;AAAA,MACV;AAED,eAAS,SAAS,OAAO,OAAO,aAAa;AAE7C,eAAS;AACT,cAAQ;AAAA,IACT;AAID,oBAAgB,CAAC,oBAAoB,CAAC,oBAAoB,qBAAqB,CAAC;AAChF,oBAAgB,CAAC,oBAAoB,oBAAoB,qBAAqB,CAAC;AAC/E,oBAAgB,oBAAoB,oBAAoB,qBAAqB,CAAC;AAI9E,sBAAkB,cAAc;AAEhC,QAAI,mBAAmB;AAAgB,WAAK,SAAS,CAAC,EAAE,UAAU;AAAA,EACnE;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,CAAC,EAAE,QAAS;AAC1B,SAAK,SAAS,CAAC,EAAE,QAAS;AAAA,EAC3B;AACH;"}