{"version": 3, "file": "TexturePass.js", "sources": ["../../src/postprocessing/TexturePass.js"], "sourcesContent": ["import { ShaderMaterial, UniformsUtils } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass TexturePass extends Pass {\n  constructor(map, opacity) {\n    super()\n\n    const shader = CopyShader\n\n    this.map = map\n    this.opacity = opacity !== undefined ? opacity : 1.0\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true,\n    })\n\n    this.needsSwap = false\n\n    this.fsQuad = new FullScreenQuad(null)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */) {\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    this.fsQuad.material = this.material\n\n    this.uniforms['opacity'].value = this.opacity\n    this.uniforms['tDiffuse'].value = this.map\n    this.material.transparent = this.opacity < 1.0\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n\n    renderer.autoClear = oldAutoClear\n  }\n\n  dispose() {\n    this.material.dispose()\n\n    this.fsQuad.dispose()\n  }\n}\n\nexport { TexturePass }\n"], "names": [], "mappings": ";;;AAIA,MAAM,oBAAoB,KAAK;AAAA,EAC7B,YAAY,KAAK,SAAS;AACxB,UAAO;AAEP,UAAM,SAAS;AAEf,SAAK,MAAM;AACX,SAAK,UAAU,YAAY,SAAY,UAAU;AAEjD,SAAK,WAAW,cAAc,MAAM,OAAO,QAAQ;AAEnD,SAAK,WAAW,IAAI,eAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc,OAAO;AAAA,MACrB,gBAAgB,OAAO;AAAA,MACvB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,oBAAoB;AAAA,IAC1B,CAAK;AAED,SAAK,YAAY;AAEjB,SAAK,SAAS,IAAI,eAAe,IAAI;AAAA,EACtC;AAAA,EAED,OAAO,UAAU,aAAa,YAAyC;AACrE,UAAM,eAAe,SAAS;AAC9B,aAAS,YAAY;AAErB,SAAK,OAAO,WAAW,KAAK;AAE5B,SAAK,SAAS,SAAS,EAAE,QAAQ,KAAK;AACtC,SAAK,SAAS,UAAU,EAAE,QAAQ,KAAK;AACvC,SAAK,SAAS,cAAc,KAAK,UAAU;AAE3C,aAAS,gBAAgB,KAAK,iBAAiB,OAAO,UAAU;AAChE,QAAI,KAAK;AAAO,eAAS,MAAO;AAChC,SAAK,OAAO,OAAO,QAAQ;AAE3B,aAAS,YAAY;AAAA,EACtB;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AAEvB,SAAK,OAAO,QAAS;AAAA,EACtB;AACH;"}