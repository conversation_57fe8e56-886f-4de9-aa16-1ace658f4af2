{"version": 3, "file": "XRHandPrimitiveModel.cjs", "sources": ["../../src/webxr/XRHandPrimitiveModel.js"], "sourcesContent": ["import {\n  DynamicDrawUsage,\n  SphereGeometry,\n  BoxGeometry,\n  MeshStandardMaterial,\n  InstancedMesh,\n  Matrix4,\n  Vector3,\n} from 'three'\n\nconst _matrix = /* @__PURE__ */ new Matrix4()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass XRHandPrimitiveModel {\n  constructor(handModel, controller, path, handedness, options) {\n    this.controller = controller\n    this.handModel = handModel\n    this.envMap = null\n\n    let geometry\n\n    if (!options || !options.primitive || options.primitive === 'sphere') {\n      geometry = new SphereGeometry(1, 10, 10)\n    } else if (options.primitive === 'box') {\n      geometry = new BoxGeometry(1, 1, 1)\n    }\n\n    const material = new MeshStandardMaterial()\n\n    this.handMesh = new InstancedMesh(geometry, material, 30)\n    this.handMesh.instanceMatrix.setUsage(DynamicDrawUsage) // will be updated every frame\n    this.handMesh.castShadow = true\n    this.handMesh.receiveShadow = true\n    this.handModel.add(this.handMesh)\n\n    this.joints = [\n      'wrist',\n      'thumb-metacarpal',\n      'thumb-phalanx-proximal',\n      'thumb-phalanx-distal',\n      'thumb-tip',\n      'index-finger-metacarpal',\n      'index-finger-phalanx-proximal',\n      'index-finger-phalanx-intermediate',\n      'index-finger-phalanx-distal',\n      'index-finger-tip',\n      'middle-finger-metacarpal',\n      'middle-finger-phalanx-proximal',\n      'middle-finger-phalanx-intermediate',\n      'middle-finger-phalanx-distal',\n      'middle-finger-tip',\n      'ring-finger-metacarpal',\n      'ring-finger-phalanx-proximal',\n      'ring-finger-phalanx-intermediate',\n      'ring-finger-phalanx-distal',\n      'ring-finger-tip',\n      'pinky-finger-metacarpal',\n      'pinky-finger-phalanx-proximal',\n      'pinky-finger-phalanx-intermediate',\n      'pinky-finger-phalanx-distal',\n      'pinky-finger-tip',\n    ]\n  }\n\n  updateMesh() {\n    const defaultRadius = 0.008\n    const joints = this.controller.joints\n\n    let count = 0\n\n    for (let i = 0; i < this.joints.length; i++) {\n      const joint = joints[this.joints[i]]\n\n      if (joint.visible) {\n        _vector.setScalar(joint.jointRadius || defaultRadius)\n        _matrix.compose(joint.position, joint.quaternion, _vector)\n        this.handMesh.setMatrixAt(i, _matrix)\n\n        count++\n      }\n    }\n\n    this.handMesh.count = count\n    this.handMesh.instanceMatrix.needsUpdate = true\n  }\n}\n\nexport { XRHandPrimitiveModel }\n"], "names": ["Matrix4", "Vector3", "SphereGeometry", "BoxGeometry", "MeshStandardMaterial", "In<PERSON>d<PERSON>esh", "DynamicDrawUsage"], "mappings": ";;;AAUA,MAAM,UAA0B,oBAAIA,MAAAA,QAAS;AAC7C,MAAM,UAA0B,oBAAIC,MAAAA,QAAS;AAE7C,MAAM,qBAAqB;AAAA,EACzB,YAAY,WAAW,YAAY,MAAM,YAAY,SAAS;AAC5D,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS;AAEd,QAAI;AAEJ,QAAI,CAAC,WAAW,CAAC,QAAQ,aAAa,QAAQ,cAAc,UAAU;AACpE,iBAAW,IAAIC,MAAc,eAAC,GAAG,IAAI,EAAE;AAAA,IAC7C,WAAe,QAAQ,cAAc,OAAO;AACtC,iBAAW,IAAIC,MAAW,YAAC,GAAG,GAAG,CAAC;AAAA,IACnC;AAED,UAAM,WAAW,IAAIC,2BAAsB;AAE3C,SAAK,WAAW,IAAIC,MAAAA,cAAc,UAAU,UAAU,EAAE;AACxD,SAAK,SAAS,eAAe,SAASC,MAAAA,gBAAgB;AACtD,SAAK,SAAS,aAAa;AAC3B,SAAK,SAAS,gBAAgB;AAC9B,SAAK,UAAU,IAAI,KAAK,QAAQ;AAEhC,SAAK,SAAS;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,aAAa;AACX,UAAM,gBAAgB;AACtB,UAAM,SAAS,KAAK,WAAW;AAE/B,QAAI,QAAQ;AAEZ,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,YAAM,QAAQ,OAAO,KAAK,OAAO,CAAC,CAAC;AAEnC,UAAI,MAAM,SAAS;AACjB,gBAAQ,UAAU,MAAM,eAAe,aAAa;AACpD,gBAAQ,QAAQ,MAAM,UAAU,MAAM,YAAY,OAAO;AACzD,aAAK,SAAS,YAAY,GAAG,OAAO;AAEpC;AAAA,MACD;AAAA,IACF;AAED,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,eAAe,cAAc;AAAA,EAC5C;AACH;;"}