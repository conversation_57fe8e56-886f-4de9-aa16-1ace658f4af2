{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "lucide-react": "^0.534.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.4.5", "framer-motion": "^12.23.12", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5"}}