module.exports = {

"[project]/src/components/ui/image-ripple.tsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_react-reconciler_4b3d0196._.js",
  "server/chunks/ssr/node_modules_three_build_three_core_a75ea919.js",
  "server/chunks/ssr/node_modules_three_build_three_module_bc28cc46.js",
  "server/chunks/ssr/node_modules_three_build_three_module_c9a5c6ae.js",
  "server/chunks/ssr/node_modules_@react-three_fiber_dist_92f5a225._.js",
  "server/chunks/ssr/node_modules_048d95ff._.js",
  "server/chunks/ssr/src_components_ui_image-ripple_tsx_dfd4f165._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/image-ripple.tsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}),

};