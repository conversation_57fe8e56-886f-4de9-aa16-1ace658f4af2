{"version": 3, "file": "PointerLockControls.js", "sources": ["../../src/controls/PointerLockControls.ts"], "sourcesContent": ["import { <PERSON>ule<PERSON>, <PERSON>, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nconst _euler = /* @__PURE__ */ new Euler(0, 0, 0, 'YXZ')\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _changeEvent = { type: 'change' }\nconst _lockEvent = { type: 'lock' }\nconst _unlockEvent = { type: 'unlock' }\nconst _MOUSE_SENSITIVITY = 0.002\nconst _PI_2 = Math.PI / 2\n\nexport interface PointerLockControlsEventMap {\n  /**\n   * Fires when the user moves the mouse.\n   */\n  change: {}\n\n  /**\n   * Fires when the pointer lock status is \"locked\" (in other words: the mouse is captured).\n   */\n  lock: {}\n\n  /**\n   * Fires when the pointer lock status is \"unlocked\" (in other words: the mouse is not captured anymore).\n   */\n  unlock: {}\n}\n\nclass PointerLockControls extends EventDispatcher<PointerLockControlsEventMap> {\n  public camera: Camera\n  public domElement?: HTMLElement\n  public isLocked: boolean\n  public minPolarAngle: number\n  public maxPolarAngle: number\n  public pointerSpeed: number\n\n  constructor(camera: Camera, domElement?: HTMLElement) {\n    super()\n\n    this.camera = camera\n    this.domElement = domElement\n    this.isLocked = false\n\n    // Set to constrain the pitch of the camera\n    // Range is 0 to Math.PI radians\n    this.minPolarAngle = 0 // radians\n    this.maxPolarAngle = Math.PI // radians\n\n    this.pointerSpeed = 1.0\n    if (domElement) this.connect(domElement)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (!this.domElement || this.isLocked === false) return\n    _euler.setFromQuaternion(this.camera.quaternion)\n    _euler.y -= event.movementX * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x -= event.movementY * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x = Math.max(_PI_2 - this.maxPolarAngle, Math.min(_PI_2 - this.minPolarAngle, _euler.x))\n    this.camera.quaternion.setFromEuler(_euler)\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onPointerlockChange = (): void => {\n    if (!this.domElement) return\n    if (this.domElement.ownerDocument.pointerLockElement === this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_lockEvent)\n      this.isLocked = true\n    } else {\n      // @ts-ignore\n      this.dispatchEvent(_unlockEvent)\n      this.isLocked = false\n    }\n  }\n\n  private onPointerlockError = (): void => {\n    console.error('THREE.PointerLockControls: Unable to use Pointer Lock API')\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    this.domElement = domElement || this.domElement\n    if (!this.domElement) return\n    this.domElement.ownerDocument.addEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.addEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.addEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public disconnect = (): void => {\n    if (!this.domElement) return\n    this.domElement.ownerDocument.removeEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.removeEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.removeEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public dispose = (): void => {\n    this.disconnect()\n  }\n\n  public getObject = (): Camera => {\n    // retaining this method for backward compatibility\n    return this.camera\n  }\n\n  private direction = new Vector3(0, 0, -1)\n  public getDirection = (v: Vector3): Vector3 => {\n    return v.copy(this.direction).applyQuaternion(this.camera.quaternion)\n  }\n\n  public moveForward = (distance: number): void => {\n    // move forward parallel to the xz-plane\n    // assumes camera.up is y-up\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    _vector.crossVectors(this.camera.up, _vector)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public moveRight = (distance: number): void => {\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public lock = (): void => {\n    if (this.domElement) this.domElement.requestPointerLock()\n  }\n\n  public unlock = (): void => {\n    if (this.domElement) this.domElement.ownerDocument.exitPointerLock()\n  }\n}\n\nexport { PointerLockControls }\n"], "names": [], "mappings": ";;;;;;;;AAGA,MAAM,SAA6B,oBAAA,MAAM,GAAG,GAAG,GAAG,KAAK;AACvD,MAAM,8BAA8B;AACpC,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,aAAa,EAAE,MAAM;AAC3B,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,QAAQ,KAAK,KAAK;AAmBxB,MAAM,4BAA4B,gBAA6C;AAAA,EAQ7E,YAAY,QAAgB,YAA0B;AAC9C;AARD;AACA;AACA;AACA;AACA;AACA;AAkBC,uCAAc,CAAC,UAA4B;AACjD,UAAI,CAAC,KAAK,cAAc,KAAK,aAAa;AAAO;AAC1C,aAAA,kBAAkB,KAAK,OAAO,UAAU;AAC/C,aAAO,KAAK,MAAM,YAAY,qBAAqB,KAAK;AACxD,aAAO,KAAK,MAAM,YAAY,qBAAqB,KAAK;AACxD,aAAO,IAAI,KAAK,IAAI,QAAQ,KAAK,eAAe,KAAK,IAAI,QAAQ,KAAK,eAAe,OAAO,CAAC,CAAC;AACzF,WAAA,OAAO,WAAW,aAAa,MAAM;AAE1C,WAAK,cAAc,YAAY;AAAA,IAAA;AAGzB,+CAAsB,MAAY;AACxC,UAAI,CAAC,KAAK;AAAY;AACtB,UAAI,KAAK,WAAW,cAAc,uBAAuB,KAAK,YAAY;AAExE,aAAK,cAAc,UAAU;AAC7B,aAAK,WAAW;AAAA,MAAA,OACX;AAEL,aAAK,cAAc,YAAY;AAC/B,aAAK,WAAW;AAAA,MAClB;AAAA,IAAA;AAGM,8CAAqB,MAAY;AACvC,cAAQ,MAAM,2DAA2D;AAAA,IAAA;AAGpE,mCAAU,CAAC,eAAkC;AAC7C,WAAA,aAAa,cAAc,KAAK;AACrC,UAAI,CAAC,KAAK;AAAY;AACtB,WAAK,WAAW,cAAc,iBAAiB,aAAa,KAAK,WAAW;AAC5E,WAAK,WAAW,cAAc,iBAAiB,qBAAqB,KAAK,mBAAmB;AAC5F,WAAK,WAAW,cAAc,iBAAiB,oBAAoB,KAAK,kBAAkB;AAAA,IAAA;AAGrF,sCAAa,MAAY;AAC9B,UAAI,CAAC,KAAK;AAAY;AACtB,WAAK,WAAW,cAAc,oBAAoB,aAAa,KAAK,WAAW;AAC/E,WAAK,WAAW,cAAc,oBAAoB,qBAAqB,KAAK,mBAAmB;AAC/F,WAAK,WAAW,cAAc,oBAAoB,oBAAoB,KAAK,kBAAkB;AAAA,IAAA;AAGxF,mCAAU,MAAY;AAC3B,WAAK,WAAW;AAAA,IAAA;AAGX,qCAAY,MAAc;AAE/B,aAAO,KAAK;AAAA,IAAA;AAGN,qCAAY,IAAI,QAAQ,GAAG,GAAG,EAAE;AACjC,wCAAe,CAAC,MAAwB;AACtC,aAAA,EAAE,KAAK,KAAK,SAAS,EAAE,gBAAgB,KAAK,OAAO,UAAU;AAAA,IAAA;AAG/D,uCAAc,CAAC,aAA2B;AAG/C,cAAQ,oBAAoB,KAAK,OAAO,QAAQ,CAAC;AACjD,cAAQ,aAAa,KAAK,OAAO,IAAI,OAAO;AAC5C,WAAK,OAAO,SAAS,gBAAgB,SAAS,QAAQ;AAAA,IAAA;AAGjD,qCAAY,CAAC,aAA2B;AAC7C,cAAQ,oBAAoB,KAAK,OAAO,QAAQ,CAAC;AACjD,WAAK,OAAO,SAAS,gBAAgB,SAAS,QAAQ;AAAA,IAAA;AAGjD,gCAAO,MAAY;AACxB,UAAI,KAAK;AAAY,aAAK,WAAW;IAAmB;AAGnD,kCAAS,MAAY;AAC1B,UAAI,KAAK;AAAiB,aAAA,WAAW,cAAc;IAAgB;AAxFnE,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW;AAIhB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,KAAK;AAE1B,SAAK,eAAe;AAChB,QAAA;AAAY,WAAK,QAAQ,UAAU;AAAA,EACzC;AA+EF;"}