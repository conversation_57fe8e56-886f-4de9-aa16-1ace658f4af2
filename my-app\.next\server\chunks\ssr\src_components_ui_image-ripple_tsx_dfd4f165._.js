module.exports = {

"[project]/src/components/ui/image-ripple.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const e = new Error("Could not parse module '[project]/src/components/ui/image-ripple.tsx'");
e.code = 'MODULE_UNPARSABLE';
throw e;
}}),
"[project]/src/components/ui/image-ripple.tsx [app-ssr] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/ui/image-ripple.tsx [app-ssr] (ecmascript)"));
}),

};