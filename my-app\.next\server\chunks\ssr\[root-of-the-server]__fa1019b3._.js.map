{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/text-effect-flipper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/text-effect-flipper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/text-effect-flipper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/text-effect-flipper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div>\r\n      <FlipLink href=\"hello.com\"><PERSON><PERSON> Chauh<PERSON></FlipLink>\r\n      <h1 className=\"text-black text-7xl flex items-center justify-center\">\r\n        I'm\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-20 rounded-full mx-2 object-cover\"\r\n        />\r\n        Parth Chauhan\r\n      </h1>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;;0BACC,8OAAC,qJAAA,CAAA,UAAQ;gBAAC,MAAK;0BAAY;;;;;;0BAC3B,8OAAC;gBAAG,WAAU;;oBAAuD;kCAEnE,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nexport const Navbar = () => {\r\n  return (\r\n    <div className=\"flex justify-between p-4 bg-gray-500 text-white\">\r\n\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,SAAS;IACpB,qBACE,8OAAC;QAAI,WAAU;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/image-ripple.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/image-ripple.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/image-ripple.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/image-ripple.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/image-ripple.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/image-ripple.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import HomePage from \"@/components/ui/homePage\";\nimport { Navbar } from \"@/components/ui/navbar\";\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\nimport React from \"react\";\nimport Scene from \"@/components/ui/image-ripple\";\n\n\nexport default function page() {\n  return (\n    <div>\n      <Navbar />\n      <HomePage/>\n      <Scene/>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAGA;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC,kIAAA,CAAA,SAAM;;;;;0BACP,8OAAC,oIAAA,CAAA,UAAQ;;;;;0BACT,8OAAC,2IAAA,CAAA,UAAK;;;;;;;;;;;AAGZ", "debugId": null}}]}