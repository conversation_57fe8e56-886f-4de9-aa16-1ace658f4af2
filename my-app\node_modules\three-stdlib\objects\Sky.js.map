{"version": 3, "file": "Sky.js", "sources": ["../../src/objects/Sky.js"], "sourcesContent": ["import { BackSide, BoxGeometry, Mesh, ShaderMaterial, UniformsUtils, Vector3 } from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Based on \"A Practical Analytic Model for Daylight\"\n * aka The Preetham Model, the de facto standard analytic skydome model\n * https://www.researchgate.net/publication/220720443_A_Practical_Analytic_Model_for_Daylight\n *\n * First implemented by <PERSON>\n * http://www.simonwallner.at/projects/atmospheric-scattering\n *\n * Improved by <PERSON>\n * http://blenderartists.org/forum/showthread.php?245954-preethams-sky-impementation-HDR\n *\n * Three.js integration by zz85 http://twitter.com/blurspline\n */\nconst Sky = /* @__PURE__ */ (() => {\n  const SkyShader = {\n    uniforms: {\n      turbidity: { value: 2 },\n      rayleigh: { value: 1 },\n      mieCoefficient: { value: 0.005 },\n      mieDirectionalG: { value: 0.8 },\n      sunPosition: { value: new Vector3() },\n      up: { value: new Vector3(0, 1, 0) },\n    },\n\n    vertexShader: /* glsl */ `\n      uniform vec3 sunPosition;\n      uniform float rayleigh;\n      uniform float turbidity;\n      uniform float mieCoefficient;\n      uniform vec3 up;\n\n      varying vec3 vWorldPosition;\n      varying vec3 vSunDirection;\n      varying float vSunfade;\n      varying vec3 vBetaR;\n      varying vec3 vBetaM;\n      varying float vSunE;\n\n      // constants for atmospheric scattering\n      const float e = 2.71828182845904523536028747135266249775724709369995957;\n      const float pi = 3.141592653589793238462643383279502884197169;\n\n      // wavelength of used primaries, according to preetham\n      const vec3 lambda = vec3( 680E-9, 550E-9, 450E-9 );\n      // this pre-calcuation replaces older TotalRayleigh(vec3 lambda) function:\n      // (8.0 * pow(pi, 3.0) * pow(pow(n, 2.0) - 1.0, 2.0) * (6.0 + 3.0 * pn)) / (3.0 * N * pow(lambda, vec3(4.0)) * (6.0 - 7.0 * pn))\n      const vec3 totalRayleigh = vec3( 5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5 );\n\n      // mie stuff\n      // K coefficient for the primaries\n      const float v = 4.0;\n      const vec3 K = vec3( 0.686, 0.678, 0.666 );\n      // MieConst = pi * pow( ( 2.0 * pi ) / lambda, vec3( v - 2.0 ) ) * K\n      const vec3 MieConst = vec3( 1.8399918514433978E14, 2.7798023919660528E14, 4.0790479543861094E14 );\n\n      // earth shadow hack\n      // cutoffAngle = pi / 1.95;\n      const float cutoffAngle = 1.6110731556870734;\n      const float steepness = 1.5;\n      const float EE = 1000.0;\n\n      float sunIntensity( float zenithAngleCos ) {\n        zenithAngleCos = clamp( zenithAngleCos, -1.0, 1.0 );\n        return EE * max( 0.0, 1.0 - pow( e, -( ( cutoffAngle - acos( zenithAngleCos ) ) / steepness ) ) );\n      }\n\n      vec3 totalMie( float T ) {\n        float c = ( 0.2 * T ) * 10E-18;\n        return 0.434 * c * MieConst;\n      }\n\n      void main() {\n\n        vec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n        vWorldPosition = worldPosition.xyz;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        gl_Position.z = gl_Position.w; // set z to camera.far\n\n        vSunDirection = normalize( sunPosition );\n\n        vSunE = sunIntensity( dot( vSunDirection, up ) );\n\n        vSunfade = 1.0 - clamp( 1.0 - exp( ( sunPosition.y / 450000.0 ) ), 0.0, 1.0 );\n\n        float rayleighCoefficient = rayleigh - ( 1.0 * ( 1.0 - vSunfade ) );\n\n      // extinction (absorbtion + out scattering)\n      // rayleigh coefficients\n        vBetaR = totalRayleigh * rayleighCoefficient;\n\n      // mie coefficients\n        vBetaM = totalMie( turbidity ) * mieCoefficient;\n\n      }\n    `,\n\n    fragmentShader: /* glsl */ `\n      varying vec3 vWorldPosition;\n      varying vec3 vSunDirection;\n      varying float vSunfade;\n      varying vec3 vBetaR;\n      varying vec3 vBetaM;\n      varying float vSunE;\n\n      uniform float mieDirectionalG;\n      uniform vec3 up;\n\n      const vec3 cameraPos = vec3( 0.0, 0.0, 0.0 );\n\n      // constants for atmospheric scattering\n      const float pi = 3.141592653589793238462643383279502884197169;\n\n      const float n = 1.0003; // refractive index of air\n      const float N = 2.545E25; // number of molecules per unit volume for air at 288.15K and 1013mb (sea level -45 celsius)\n\n      // optical length at zenith for molecules\n      const float rayleighZenithLength = 8.4E3;\n      const float mieZenithLength = 1.25E3;\n      // 66 arc seconds -> degrees, and the cosine of that\n      const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;\n\n      // 3.0 / ( 16.0 * pi )\n      const float THREE_OVER_SIXTEENPI = 0.05968310365946075;\n      // 1.0 / ( 4.0 * pi )\n      const float ONE_OVER_FOURPI = 0.07957747154594767;\n\n      float rayleighPhase( float cosTheta ) {\n        return THREE_OVER_SIXTEENPI * ( 1.0 + pow( cosTheta, 2.0 ) );\n      }\n\n      float hgPhase( float cosTheta, float g ) {\n        float g2 = pow( g, 2.0 );\n        float inverse = 1.0 / pow( 1.0 - 2.0 * g * cosTheta + g2, 1.5 );\n        return ONE_OVER_FOURPI * ( ( 1.0 - g2 ) * inverse );\n      }\n\n      void main() {\n\n        vec3 direction = normalize( vWorldPosition - cameraPos );\n\n      // optical length\n      // cutoff angle at 90 to avoid singularity in next formula.\n        float zenithAngle = acos( max( 0.0, dot( up, direction ) ) );\n        float inverse = 1.0 / ( cos( zenithAngle ) + 0.15 * pow( 93.885 - ( ( zenithAngle * 180.0 ) / pi ), -1.253 ) );\n        float sR = rayleighZenithLength * inverse;\n        float sM = mieZenithLength * inverse;\n\n      // combined extinction factor\n        vec3 Fex = exp( -( vBetaR * sR + vBetaM * sM ) );\n\n      // in scattering\n        float cosTheta = dot( direction, vSunDirection );\n\n        float rPhase = rayleighPhase( cosTheta * 0.5 + 0.5 );\n        vec3 betaRTheta = vBetaR * rPhase;\n\n        float mPhase = hgPhase( cosTheta, mieDirectionalG );\n        vec3 betaMTheta = vBetaM * mPhase;\n\n        vec3 Lin = pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * ( 1.0 - Fex ), vec3( 1.5 ) );\n        Lin *= mix( vec3( 1.0 ), pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * Fex, vec3( 1.0 / 2.0 ) ), clamp( pow( 1.0 - dot( up, vSunDirection ), 5.0 ), 0.0, 1.0 ) );\n\n      // nightsky\n        float theta = acos( direction.y ); // elevation --> y-axis, [-pi/2, pi/2]\n        float phi = atan( direction.z, direction.x ); // azimuth --> x-axis [-pi/2, pi/2]\n        vec2 uv = vec2( phi, theta ) / vec2( 2.0 * pi, pi ) + vec2( 0.5, 0.0 );\n        vec3 L0 = vec3( 0.1 ) * Fex;\n\n      // composition + solar disc\n        float sundisk = smoothstep( sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta );\n        L0 += ( vSunE * 19000.0 * Fex ) * sundisk;\n\n        vec3 texColor = ( Lin + L0 ) * 0.04 + vec3( 0.0, 0.0003, 0.00075 );\n\n        vec3 retColor = pow( texColor, vec3( 1.0 / ( 1.2 + ( 1.2 * vSunfade ) ) ) );\n\n        gl_FragColor = vec4( retColor, 1.0 );\n\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\n      }\n    `,\n  }\n\n  const material = new ShaderMaterial({\n    name: 'SkyShader',\n    fragmentShader: SkyShader.fragmentShader,\n    vertexShader: SkyShader.vertexShader,\n    uniforms: UniformsUtils.clone(SkyShader.uniforms),\n    side: BackSide,\n    depthWrite: false,\n  })\n\n  class Sky extends Mesh {\n    constructor() {\n      super(new BoxGeometry(1, 1, 1), material)\n    }\n\n    static SkyShader = SkyShader\n    static material = material\n  }\n\n  return Sky\n})()\n\nexport { Sky }\n"], "names": ["Sky"], "mappings": ";;;;;;;;AAgBK,MAAC,MAAuB,uBAAM;AACjC,QAAM,YAAY;AAAA,IAChB,UAAU;AAAA,MACR,WAAW,EAAE,OAAO,EAAG;AAAA,MACvB,UAAU,EAAE,OAAO,EAAG;AAAA,MACtB,gBAAgB,EAAE,OAAO,KAAO;AAAA,MAChC,iBAAiB,EAAE,OAAO,IAAK;AAAA,MAC/B,aAAa,EAAE,OAAO,IAAI,UAAW;AAAA,MACrC,IAAI,EAAE,OAAO,IAAI,QAAQ,GAAG,GAAG,CAAC,EAAG;AAAA,IACpC;AAAA,IAED;AAAA;AAAA,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAyEzB;AAAA;AAAA,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAmFb,WAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAIxD;AAED,QAAM,WAAW,IAAI,eAAe;AAAA,IAClC,MAAM;AAAA,IACN,gBAAgB,UAAU;AAAA,IAC1B,cAAc,UAAU;AAAA,IACxB,UAAU,cAAc,MAAM,UAAU,QAAQ;AAAA,IAChD,MAAM;AAAA,IACN,YAAY;AAAA,EAChB,CAAG;AAED,QAAMA,aAAY,KAAK;AAAA,IACrB,cAAc;AACZ,YAAM,IAAI,YAAY,GAAG,GAAG,CAAC,GAAG,QAAQ;AAAA,IACzC;AAAA,EAIF;AAFC,gBALIA,MAKG,aAAY;AACnB,gBANIA,MAMG,YAAW;AAGpB,SAAOA;AACT,GAAC;"}