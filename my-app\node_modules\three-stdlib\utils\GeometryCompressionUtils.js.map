{"version": 3, "file": "GeometryCompressionUtils.js", "sources": ["../../src/utils/GeometryCompressionUtils.js"], "sourcesContent": ["/**\n * Octahedron and Quantization encodings based on work by:\n *\n * @link https://github.com/tsherif/mesh-quantization-example\n *\n */\n\nimport {\n  BufferAttribute,\n  Matrix3,\n  Matrix4,\n  MeshPhongMaterial,\n  ShaderChunk,\n  ShaderLib,\n  UniformsUtils,\n  Vector3,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nvar GeometryCompressionUtils = {\n  /**\n   * Make the input mesh.geometry's normal attribute encoded and compressed by 3 different methods.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the normal data.\n   *\n   * @param {THREE.Mesh} mesh\n   * @param {String} encodeMethod\t\t\"DEFAULT\" || \"OCT1Byte\" || \"OCT2Byte\" || \"ANGLES\"\n   *\n   */\n  compressNormals: function (mesh, encodeMethod) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry. ')\n    }\n\n    const normal = mesh.geometry.attributes.normal\n\n    if (!normal) {\n      console.error('Geometry must contain normal attribute. ')\n    }\n\n    if (normal.isPacked) return\n\n    if (normal.itemSize != 3) {\n      console.error('normal.itemSize is not 3, which cannot be encoded. ')\n    }\n\n    const array = normal.array\n    const count = normal.count\n\n    let result\n    if (encodeMethod == 'DEFAULT') {\n      // TODO: Add 1 byte to the result, making the encoded length to be 4 bytes.\n      result = new Uint8Array(count * 3)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[idx], array[idx + 1], array[idx + 2], 1)\n\n        result[idx + 0] = encoded[0]\n        result[idx + 1] = encoded[1]\n        result[idx + 2] = encoded[2]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 3, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 1\n    } else if (encodeMethod == 'OCT1Byte') {\n      /**\n       * It is not recommended to use 1-byte octahedron normals encoding unless you want to extremely reduce the memory usage\n       * As it makes vertex data not aligned to a 4 byte boundary which may harm some WebGL implementations and sometimes the normal distortion is visible\n       * Please refer to @zeux 's comments in https://github.com/mrdoob/three.js/pull/18208\n       */\n\n      result = new Int8Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 1)\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 1\n    } else if (encodeMethod == 'OCT2Byte') {\n      result = new Int16Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.octEncodeBest(array[idx], array[idx + 1], array[idx + 2], 2)\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 2\n    } else if (encodeMethod == 'ANGLES') {\n      result = new Uint16Array(count * 2)\n\n      for (let idx = 0; idx < array.length; idx += 3) {\n        const encoded = this.EncodingFuncs.anglesEncode(array[idx], array[idx + 1], array[idx + 2])\n\n        result[(idx / 3) * 2 + 0] = encoded[0]\n        result[(idx / 3) * 2 + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('normal', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.normal.bytes = result.length * 2\n    } else {\n      console.error('Unrecognized encoding method, should be `DEFAULT` or `ANGLES` or `OCT`. ')\n    }\n\n    mesh.geometry.attributes.normal.needsUpdate = true\n    mesh.geometry.attributes.normal.isPacked = true\n    mesh.geometry.attributes.normal.packingMethod = encodeMethod\n\n    // modify material\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material)\n    }\n\n    if (encodeMethod == 'ANGLES') {\n      mesh.material.defines.USE_PACKED_NORMAL = 0\n    }\n\n    if (encodeMethod == 'OCT1Byte') {\n      mesh.material.defines.USE_PACKED_NORMAL = 1\n    }\n\n    if (encodeMethod == 'OCT2Byte') {\n      mesh.material.defines.USE_PACKED_NORMAL = 1\n    }\n\n    if (encodeMethod == 'DEFAULT') {\n      mesh.material.defines.USE_PACKED_NORMAL = 2\n    }\n  },\n\n  /**\n   * Make the input mesh.geometry's position attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the position data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressPositions: function (mesh) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry. ')\n    }\n\n    const position = mesh.geometry.attributes.position\n\n    if (!position) {\n      console.error('Geometry must contain position attribute. ')\n    }\n\n    if (position.isPacked) return\n\n    if (position.itemSize != 3) {\n      console.error('position.itemSize is not 3, which cannot be packed. ')\n    }\n\n    const array = position.array\n    const encodingBytes = 2\n\n    const result = this.EncodingFuncs.quantizedEncode(array, encodingBytes)\n\n    const quantized = result.quantized\n    const decodeMat = result.decodeMat\n\n    // IMPORTANT: calculate original geometry bounding info first, before updating packed positions\n    if (mesh.geometry.boundingBox == null) mesh.geometry.computeBoundingBox()\n    if (mesh.geometry.boundingSphere == null) mesh.geometry.computeBoundingSphere()\n\n    mesh.geometry.setAttribute('position', new BufferAttribute(quantized, 3))\n    mesh.geometry.attributes.position.isPacked = true\n    mesh.geometry.attributes.position.needsUpdate = true\n    mesh.geometry.attributes.position.bytes = quantized.length * encodingBytes\n\n    // modify material\n    if (!(mesh.material instanceof PackedPhongMaterial)) {\n      mesh.material = new PackedPhongMaterial().copy(mesh.material)\n    }\n\n    mesh.material.defines.USE_PACKED_POSITION = 0\n\n    mesh.material.uniforms.quantizeMatPos.value = decodeMat\n    mesh.material.uniforms.quantizeMatPos.needsUpdate = true\n  },\n\n  /**\n   * Make the input mesh.geometry's uv attribute encoded and compressed.\n   * Also will change the mesh.material to `PackedPhongMaterial` which let the vertex shader program decode the uv data.\n   *\n   * @param {THREE.Mesh} mesh\n   *\n   */\n  compressUvs: function (mesh) {\n    if (!mesh.geometry) {\n      console.error('Mesh must contain geometry property. ')\n    }\n\n    const uvs = mesh.geometry.attributes.uv\n\n    if (!uvs) {\n      console.error('Geometry must contain uv attribute. ')\n    }\n\n    if (uvs.isPacked) return\n\n    const range = { min: Infinity, max: -Infinity }\n\n    const array = uvs.array\n\n    for (let i = 0; i < array.length; i++) {\n      range.min = Math.min(range.min, array[i])\n      range.max = Math.max(range.max, array[i])\n    }\n\n    let result\n\n    if (range.min >= -1.0 && range.max <= 1.0) {\n      // use default encoding method\n      result = new Uint16Array(array.length)\n\n      for (let i = 0; i < array.length; i += 2) {\n        const encoded = this.EncodingFuncs.defaultEncode(array[i], array[i + 1], 0, 2)\n\n        result[i] = encoded[0]\n        result[i + 1] = encoded[1]\n      }\n\n      mesh.geometry.setAttribute('uv', new BufferAttribute(result, 2, true))\n      mesh.geometry.attributes.uv.isPacked = true\n      mesh.geometry.attributes.uv.needsUpdate = true\n      mesh.geometry.attributes.uv.bytes = result.length * 2\n\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material)\n      }\n\n      mesh.material.defines.USE_PACKED_UV = 0\n    } else {\n      // use quantized encoding method\n      result = this.EncodingFuncs.quantizedEncodeUV(array, 2)\n\n      mesh.geometry.setAttribute('uv', new BufferAttribute(result.quantized, 2))\n      mesh.geometry.attributes.uv.isPacked = true\n      mesh.geometry.attributes.uv.needsUpdate = true\n      mesh.geometry.attributes.uv.bytes = result.quantized.length * 2\n\n      if (!(mesh.material instanceof PackedPhongMaterial)) {\n        mesh.material = new PackedPhongMaterial().copy(mesh.material)\n      }\n\n      mesh.material.defines.USE_PACKED_UV = 1\n\n      mesh.material.uniforms.quantizeMatUV.value = result.decodeMat\n      mesh.material.uniforms.quantizeMatUV.needsUpdate = true\n    }\n  },\n\n  EncodingFuncs: {\n    defaultEncode: function (x, y, z, bytes) {\n      if (bytes == 1) {\n        const tmpx = Math.round((x + 1) * 0.5 * 255)\n        const tmpy = Math.round((y + 1) * 0.5 * 255)\n        const tmpz = Math.round((z + 1) * 0.5 * 255)\n        return new Uint8Array([tmpx, tmpy, tmpz])\n      } else if (bytes == 2) {\n        const tmpx = Math.round((x + 1) * 0.5 * 65535)\n        const tmpy = Math.round((y + 1) * 0.5 * 65535)\n        const tmpz = Math.round((z + 1) * 0.5 * 65535)\n        return new Uint16Array([tmpx, tmpy, tmpz])\n      } else {\n        console.error('number of bytes must be 1 or 2')\n      }\n    },\n\n    defaultDecode: function (array, bytes) {\n      if (bytes == 1) {\n        return [(array[0] / 255) * 2.0 - 1.0, (array[1] / 255) * 2.0 - 1.0, (array[2] / 255) * 2.0 - 1.0]\n      } else if (bytes == 2) {\n        return [(array[0] / 65535) * 2.0 - 1.0, (array[1] / 65535) * 2.0 - 1.0, (array[2] / 65535) * 2.0 - 1.0]\n      } else {\n        console.error('number of bytes must be 1 or 2')\n      }\n    },\n\n    // for `Angles` encoding\n    anglesEncode: function (x, y, z) {\n      const normal0 = parseInt(0.5 * (1.0 + Math.atan2(y, x) / Math.PI) * 65535)\n      const normal1 = parseInt(0.5 * (1.0 + z) * 65535)\n      return new Uint16Array([normal0, normal1])\n    },\n\n    // for `Octahedron` encoding\n    octEncodeBest: function (x, y, z, bytes) {\n      var oct, dec, best, currentCos, bestCos\n\n      // Test various combinations of ceil and floor\n      // to minimize rounding errors\n      best = oct = octEncodeVec3(x, y, z, 'floor', 'floor')\n      dec = octDecodeVec2(oct)\n      bestCos = dot(x, y, z, dec)\n\n      oct = octEncodeVec3(x, y, z, 'ceil', 'floor')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n        bestCos = currentCos\n      }\n\n      oct = octEncodeVec3(x, y, z, 'floor', 'ceil')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n        bestCos = currentCos\n      }\n\n      oct = octEncodeVec3(x, y, z, 'ceil', 'ceil')\n      dec = octDecodeVec2(oct)\n      currentCos = dot(x, y, z, dec)\n\n      if (currentCos > bestCos) {\n        best = oct\n      }\n\n      return best\n\n      function octEncodeVec3(x0, y0, z0, xfunc, yfunc) {\n        var x = x0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0))\n        var y = y0 / (Math.abs(x0) + Math.abs(y0) + Math.abs(z0))\n\n        if (z < 0) {\n          var tempx = (1 - Math.abs(y)) * (x >= 0 ? 1 : -1)\n          var tempy = (1 - Math.abs(x)) * (y >= 0 ? 1 : -1)\n\n          x = tempx\n          y = tempy\n\n          var diff = 1 - Math.abs(x) - Math.abs(y)\n          if (diff > 0) {\n            diff += 0.001\n            x += x > 0 ? diff / 2 : -diff / 2\n            y += y > 0 ? diff / 2 : -diff / 2\n          }\n        }\n\n        if (bytes == 1) {\n          return new Int8Array([Math[xfunc](x * 127.5 + (x < 0 ? 1 : 0)), Math[yfunc](y * 127.5 + (y < 0 ? 1 : 0))])\n        }\n\n        if (bytes == 2) {\n          return new Int16Array([\n            Math[xfunc](x * 32767.5 + (x < 0 ? 1 : 0)),\n            Math[yfunc](y * 32767.5 + (y < 0 ? 1 : 0)),\n          ])\n        }\n      }\n\n      function octDecodeVec2(oct) {\n        var x = oct[0]\n        var y = oct[1]\n\n        if (bytes == 1) {\n          x /= x < 0 ? 127 : 128\n          y /= y < 0 ? 127 : 128\n        } else if (bytes == 2) {\n          x /= x < 0 ? 32767 : 32768\n          y /= y < 0 ? 32767 : 32768\n        }\n\n        var z = 1 - Math.abs(x) - Math.abs(y)\n\n        if (z < 0) {\n          var tmpx = x\n          x = (1 - Math.abs(y)) * (x >= 0 ? 1 : -1)\n          y = (1 - Math.abs(tmpx)) * (y >= 0 ? 1 : -1)\n        }\n\n        var length = Math.sqrt(x * x + y * y + z * z)\n\n        return [x / length, y / length, z / length]\n      }\n\n      function dot(x, y, z, vec3) {\n        return x * vec3[0] + y * vec3[1] + z * vec3[2]\n      }\n    },\n\n    quantizedEncode: function (array, bytes) {\n      let quantized, segments\n\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length)\n        segments = 255\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length)\n        segments = 65535\n      } else {\n        console.error('number of bytes error! ')\n      }\n\n      const decodeMat = new Matrix4()\n\n      const min = new Float32Array(3)\n      const max = new Float32Array(3)\n\n      min[0] = min[1] = min[2] = Number.MAX_VALUE\n      max[0] = max[1] = max[2] = -Number.MAX_VALUE\n\n      for (let i = 0; i < array.length; i += 3) {\n        min[0] = Math.min(min[0], array[i + 0])\n        min[1] = Math.min(min[1], array[i + 1])\n        min[2] = Math.min(min[2], array[i + 2])\n        max[0] = Math.max(max[0], array[i + 0])\n        max[1] = Math.max(max[1], array[i + 1])\n        max[2] = Math.max(max[2], array[i + 2])\n      }\n\n      decodeMat.scale(\n        new Vector3((max[0] - min[0]) / segments, (max[1] - min[1]) / segments, (max[2] - min[2]) / segments),\n      )\n\n      decodeMat.elements[12] = min[0]\n      decodeMat.elements[13] = min[1]\n      decodeMat.elements[14] = min[2]\n\n      decodeMat.transpose()\n\n      const multiplier = new Float32Array([\n        max[0] !== min[0] ? segments / (max[0] - min[0]) : 0,\n        max[1] !== min[1] ? segments / (max[1] - min[1]) : 0,\n        max[2] !== min[2] ? segments / (max[2] - min[2]) : 0,\n      ])\n\n      for (let i = 0; i < array.length; i += 3) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0])\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1])\n        quantized[i + 2] = Math.floor((array[i + 2] - min[2]) * multiplier[2])\n      }\n\n      return {\n        quantized: quantized,\n        decodeMat: decodeMat,\n      }\n    },\n\n    quantizedEncodeUV: function (array, bytes) {\n      let quantized, segments\n\n      if (bytes == 1) {\n        quantized = new Uint8Array(array.length)\n        segments = 255\n      } else if (bytes == 2) {\n        quantized = new Uint16Array(array.length)\n        segments = 65535\n      } else {\n        console.error('number of bytes error! ')\n      }\n\n      const decodeMat = new Matrix3()\n\n      const min = new Float32Array(2)\n      const max = new Float32Array(2)\n\n      min[0] = min[1] = Number.MAX_VALUE\n      max[0] = max[1] = -Number.MAX_VALUE\n\n      for (let i = 0; i < array.length; i += 2) {\n        min[0] = Math.min(min[0], array[i + 0])\n        min[1] = Math.min(min[1], array[i + 1])\n        max[0] = Math.max(max[0], array[i + 0])\n        max[1] = Math.max(max[1], array[i + 1])\n      }\n\n      decodeMat.scale((max[0] - min[0]) / segments, (max[1] - min[1]) / segments)\n\n      decodeMat.elements[6] = min[0]\n      decodeMat.elements[7] = min[1]\n\n      decodeMat.transpose()\n\n      const multiplier = new Float32Array([\n        max[0] !== min[0] ? segments / (max[0] - min[0]) : 0,\n        max[1] !== min[1] ? segments / (max[1] - min[1]) : 0,\n      ])\n\n      for (let i = 0; i < array.length; i += 2) {\n        quantized[i + 0] = Math.floor((array[i + 0] - min[0]) * multiplier[0])\n        quantized[i + 1] = Math.floor((array[i + 1] - min[1]) * multiplier[1])\n      }\n\n      return {\n        quantized: quantized,\n        decodeMat: decodeMat,\n      }\n    },\n  },\n}\n\n/**\n * `PackedPhongMaterial` inherited from THREE.MeshPhongMaterial\n *\n * @param {Object} parameters\n */\nclass PackedPhongMaterial extends MeshPhongMaterial {\n  constructor(parameters) {\n    super()\n\n    this.defines = {}\n    this.type = 'PackedPhongMaterial'\n    this.uniforms = UniformsUtils.merge([\n      ShaderLib.phong.uniforms,\n\n      {\n        quantizeMatPos: { value: null },\n        quantizeMatUV: { value: null },\n      },\n    ])\n\n    this.vertexShader = [\n      '#define PHONG',\n\n      'varying vec3 vViewPosition;',\n\n      '#ifndef FLAT_SHADED',\n      'varying vec3 vNormal;',\n      '#endif',\n\n      ShaderChunk.common,\n      ShaderChunk.uv_pars_vertex,\n      ShaderChunk.uv2_pars_vertex,\n      ShaderChunk.displacementmap_pars_vertex,\n      ShaderChunk.envmap_pars_vertex,\n      ShaderChunk.color_pars_vertex,\n      ShaderChunk.fog_pars_vertex,\n      ShaderChunk.morphtarget_pars_vertex,\n      ShaderChunk.skinning_pars_vertex,\n      ShaderChunk.shadowmap_pars_vertex,\n      ShaderChunk.logdepthbuf_pars_vertex,\n      ShaderChunk.clipping_planes_pars_vertex,\n\n      `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\t#if USE_PACKED_NORMAL == 0\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfloat x = packedNormal.x * 2.0 - 1.0;\n\t\t\t\t\t\t\tfloat y = packedNormal.y * 2.0 - 1.0;\n\t\t\t\t\t\t\tvec2 scth = vec2(sin(x * PI), cos(x * PI));\n\t\t\t\t\t\t\tvec2 scphi = vec2(sqrt(1.0 - y * y), y);\n\t\t\t\t\t\t\treturn normalize( vec3(scth.y * scphi.x, scth.x * scphi.x, scphi.y) );\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 1\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = vec3(packedNormal.xy, 1.0 - abs(packedNormal.x) - abs(packedNormal.y));\n\t\t\t\t\t\t\tif (v.z < 0.0)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tv.xy = (1.0 - abs(v.yx)) * vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_NORMAL == 2\n\t\t\t\t\t\tvec3 decodeNormal(vec3 packedNormal)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec3 v = (packedNormal * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn normalize(v);\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\tuniform mat4 quantizeMatPos;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tuniform mat3 quantizeMatUV;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      `#ifdef USE_PACKED_UV\n\t\t\t\t\t#if USE_PACKED_UV == 0\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = (packedUV * 2.0) - 1.0;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#if USE_PACKED_UV == 1\n\t\t\t\t\t\tvec2 decodeUV(vec2 packedUV)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvec2 uv = ( vec3(packedUV, 1.0) * quantizeMatUV ).xy;\n\t\t\t\t\t\t\treturn uv;\n\t\t\t\t\t\t}\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      'void main() {',\n\n      ShaderChunk.uv_vertex,\n\n      `#ifdef USE_UV\n\t\t\t\t\t#ifdef USE_PACKED_UV\n\t\t\t\t\t\tvUv = decodeUV(vUv);\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      ShaderChunk.uv2_vertex,\n      ShaderChunk.color_vertex,\n      ShaderChunk.beginnormal_vertex,\n\n      `#ifdef USE_PACKED_NORMAL\n\t\t\t\t\tobjectNormal = decodeNormal(objectNormal);\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_TANGENT\n\t\t\t\t\tvec3 objectTangent = vec3( tangent.xyz );\n\t\t\t\t#endif\n\t\t\t\t`,\n\n      ShaderChunk.morphnormal_vertex,\n      ShaderChunk.skinbase_vertex,\n      ShaderChunk.skinnormal_vertex,\n      ShaderChunk.defaultnormal_vertex,\n\n      '#ifndef FLAT_SHADED',\n      '\tvNormal = normalize( transformedNormal );',\n      '#endif',\n\n      ShaderChunk.begin_vertex,\n\n      `#ifdef USE_PACKED_POSITION\n\t\t\t\t\t#if USE_PACKED_POSITION == 0\n\t\t\t\t\t\ttransformed = ( vec4(transformed, 1.0) * quantizeMatPos ).xyz;\n\t\t\t\t\t#endif\n\t\t\t\t#endif`,\n\n      ShaderChunk.morphtarget_vertex,\n      ShaderChunk.skinning_vertex,\n      ShaderChunk.displacementmap_vertex,\n      ShaderChunk.project_vertex,\n      ShaderChunk.logdepthbuf_vertex,\n      ShaderChunk.clipping_planes_vertex,\n\n      'vViewPosition = - mvPosition.xyz;',\n\n      ShaderChunk.worldpos_vertex,\n      ShaderChunk.envmap_vertex,\n      ShaderChunk.shadowmap_vertex,\n      ShaderChunk.fog_vertex,\n\n      '}',\n    ].join('\\n')\n\n    // Use the original MeshPhongMaterial's fragmentShader.\n    this.fragmentShader = [\n      '#define PHONG',\n\n      'uniform vec3 diffuse;',\n      'uniform vec3 emissive;',\n      'uniform vec3 specular;',\n      'uniform float shininess;',\n      'uniform float opacity;',\n\n      ShaderChunk.common,\n      ShaderChunk.packing,\n      ShaderChunk.dithering_pars_fragment,\n      ShaderChunk.color_pars_fragment,\n      ShaderChunk.uv_pars_fragment,\n      ShaderChunk.uv2_pars_fragment,\n      ShaderChunk.map_pars_fragment,\n      ShaderChunk.alphamap_pars_fragment,\n      ShaderChunk.aomap_pars_fragment,\n      ShaderChunk.lightmap_pars_fragment,\n      ShaderChunk.emissivemap_pars_fragment,\n      ShaderChunk.envmap_common_pars_fragment,\n      ShaderChunk.envmap_pars_fragment,\n      ShaderChunk.cube_uv_reflection_fragment,\n      ShaderChunk.fog_pars_fragment,\n      ShaderChunk.bsdfs,\n      ShaderChunk.lights_pars_begin,\n      ShaderChunk.lights_phong_pars_fragment,\n      ShaderChunk.shadowmap_pars_fragment,\n      ShaderChunk.bumpmap_pars_fragment,\n      ShaderChunk.normalmap_pars_fragment,\n      ShaderChunk.specularmap_pars_fragment,\n      ShaderChunk.logdepthbuf_pars_fragment,\n      ShaderChunk.clipping_planes_pars_fragment,\n\n      'void main() {',\n\n      ShaderChunk.clipping_planes_fragment,\n\n      'vec4 diffuseColor = vec4( diffuse, opacity );',\n      'ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );',\n      'vec3 totalEmissiveRadiance = emissive;',\n\n      ShaderChunk.logdepthbuf_fragment,\n      ShaderChunk.map_fragment,\n      ShaderChunk.color_fragment,\n      ShaderChunk.alphamap_fragment,\n      ShaderChunk.alphatest_fragment,\n      ShaderChunk.specularmap_fragment,\n      ShaderChunk.normal_fragment_begin,\n      ShaderChunk.normal_fragment_maps,\n      ShaderChunk.emissivemap_fragment,\n\n      // accumulation\n      ShaderChunk.lights_phong_fragment,\n      ShaderChunk.lights_fragment_begin,\n      ShaderChunk.lights_fragment_maps,\n      ShaderChunk.lights_fragment_end,\n\n      // modulation\n      ShaderChunk.aomap_fragment,\n\n      'vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;',\n\n      ShaderChunk.envmap_fragment,\n\n      'gl_FragColor = vec4( outgoingLight, diffuseColor.a );',\n\n      ShaderChunk.tonemapping_fragment,\n      version >= 154 ? ShaderChunk.colorspace_fragment : ShaderChunk.encodings_fragment,\n      ShaderChunk.fog_fragment,\n      ShaderChunk.premultiplied_alpha_fragment,\n      ShaderChunk.dithering_fragment,\n      '}',\n    ].join('\\n')\n\n    this.setValues(parameters)\n  }\n}\n\nexport { GeometryCompressionUtils, PackedPhongMaterial }\n"], "names": ["x", "y", "oct", "z"], "mappings": ";;AAmBG,IAAC,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7B,iBAAiB,SAAU,MAAM,cAAc;AAC7C,QAAI,CAAC,KAAK,UAAU;AAClB,cAAQ,MAAM,8BAA8B;AAAA,IAC7C;AAED,UAAM,SAAS,KAAK,SAAS,WAAW;AAExC,QAAI,CAAC,QAAQ;AACX,cAAQ,MAAM,0CAA0C;AAAA,IACzD;AAED,QAAI,OAAO;AAAU;AAErB,QAAI,OAAO,YAAY,GAAG;AACxB,cAAQ,MAAM,qDAAqD;AAAA,IACpE;AAED,UAAM,QAAQ,OAAO;AACrB,UAAM,QAAQ,OAAO;AAErB,QAAI;AACJ,QAAI,gBAAgB,WAAW;AAE7B,eAAS,IAAI,WAAW,QAAQ,CAAC;AAEjC,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,cAAM,UAAU,KAAK,cAAc,cAAc,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC;AAE9F,eAAO,MAAM,CAAC,IAAI,QAAQ,CAAC;AAC3B,eAAO,MAAM,CAAC,IAAI,QAAQ,CAAC;AAC3B,eAAO,MAAM,CAAC,IAAI,QAAQ,CAAC;AAAA,MAC5B;AAED,WAAK,SAAS,aAAa,UAAU,IAAI,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AACzE,WAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC9D,WAAe,gBAAgB,YAAY;AAOrC,eAAS,IAAI,UAAU,QAAQ,CAAC;AAEhC,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,cAAM,UAAU,KAAK,cAAc,cAAc,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC;AAE9F,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AACrC,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MACtC;AAED,WAAK,SAAS,aAAa,UAAU,IAAI,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AACzE,WAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC9D,WAAe,gBAAgB,YAAY;AACrC,eAAS,IAAI,WAAW,QAAQ,CAAC;AAEjC,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,cAAM,UAAU,KAAK,cAAc,cAAc,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC;AAE9F,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AACrC,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MACtC;AAED,WAAK,SAAS,aAAa,UAAU,IAAI,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AACzE,WAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC9D,WAAe,gBAAgB,UAAU;AACnC,eAAS,IAAI,YAAY,QAAQ,CAAC;AAElC,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,cAAM,UAAU,KAAK,cAAc,aAAa,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AAE1F,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AACrC,eAAQ,MAAM,IAAK,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MACtC;AAED,WAAK,SAAS,aAAa,UAAU,IAAI,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AACzE,WAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS;AAAA,IAC9D,OAAW;AACL,cAAQ,MAAM,0EAA0E;AAAA,IACzF;AAED,SAAK,SAAS,WAAW,OAAO,cAAc;AAC9C,SAAK,SAAS,WAAW,OAAO,WAAW;AAC3C,SAAK,SAAS,WAAW,OAAO,gBAAgB;AAGhD,QAAI,EAAE,KAAK,oBAAoB,sBAAsB;AACnD,WAAK,WAAW,IAAI,oBAAqB,EAAC,KAAK,KAAK,QAAQ;AAAA,IAC7D;AAED,QAAI,gBAAgB,UAAU;AAC5B,WAAK,SAAS,QAAQ,oBAAoB;AAAA,IAC3C;AAED,QAAI,gBAAgB,YAAY;AAC9B,WAAK,SAAS,QAAQ,oBAAoB;AAAA,IAC3C;AAED,QAAI,gBAAgB,YAAY;AAC9B,WAAK,SAAS,QAAQ,oBAAoB;AAAA,IAC3C;AAED,QAAI,gBAAgB,WAAW;AAC7B,WAAK,SAAS,QAAQ,oBAAoB;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,mBAAmB,SAAU,MAAM;AACjC,QAAI,CAAC,KAAK,UAAU;AAClB,cAAQ,MAAM,8BAA8B;AAAA,IAC7C;AAED,UAAM,WAAW,KAAK,SAAS,WAAW;AAE1C,QAAI,CAAC,UAAU;AACb,cAAQ,MAAM,4CAA4C;AAAA,IAC3D;AAED,QAAI,SAAS;AAAU;AAEvB,QAAI,SAAS,YAAY,GAAG;AAC1B,cAAQ,MAAM,sDAAsD;AAAA,IACrE;AAED,UAAM,QAAQ,SAAS;AACvB,UAAM,gBAAgB;AAEtB,UAAM,SAAS,KAAK,cAAc,gBAAgB,OAAO,aAAa;AAEtE,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,OAAO;AAGzB,QAAI,KAAK,SAAS,eAAe;AAAM,WAAK,SAAS,mBAAoB;AACzE,QAAI,KAAK,SAAS,kBAAkB;AAAM,WAAK,SAAS,sBAAuB;AAE/E,SAAK,SAAS,aAAa,YAAY,IAAI,gBAAgB,WAAW,CAAC,CAAC;AACxE,SAAK,SAAS,WAAW,SAAS,WAAW;AAC7C,SAAK,SAAS,WAAW,SAAS,cAAc;AAChD,SAAK,SAAS,WAAW,SAAS,QAAQ,UAAU,SAAS;AAG7D,QAAI,EAAE,KAAK,oBAAoB,sBAAsB;AACnD,WAAK,WAAW,IAAI,oBAAqB,EAAC,KAAK,KAAK,QAAQ;AAAA,IAC7D;AAED,SAAK,SAAS,QAAQ,sBAAsB;AAE5C,SAAK,SAAS,SAAS,eAAe,QAAQ;AAC9C,SAAK,SAAS,SAAS,eAAe,cAAc;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,SAAU,MAAM;AAC3B,QAAI,CAAC,KAAK,UAAU;AAClB,cAAQ,MAAM,uCAAuC;AAAA,IACtD;AAED,UAAM,MAAM,KAAK,SAAS,WAAW;AAErC,QAAI,CAAC,KAAK;AACR,cAAQ,MAAM,sCAAsC;AAAA,IACrD;AAED,QAAI,IAAI;AAAU;AAElB,UAAM,QAAQ,EAAE,KAAK,UAAU,KAAK,UAAW;AAE/C,UAAM,QAAQ,IAAI;AAElB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AACxC,YAAM,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,IACzC;AAED,QAAI;AAEJ,QAAI,MAAM,OAAO,MAAQ,MAAM,OAAO,GAAK;AAEzC,eAAS,IAAI,YAAY,MAAM,MAAM;AAErC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAM,UAAU,KAAK,cAAc,cAAc,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC;AAE7E,eAAO,CAAC,IAAI,QAAQ,CAAC;AACrB,eAAO,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MAC1B;AAED,WAAK,SAAS,aAAa,MAAM,IAAI,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AACrE,WAAK,SAAS,WAAW,GAAG,WAAW;AACvC,WAAK,SAAS,WAAW,GAAG,cAAc;AAC1C,WAAK,SAAS,WAAW,GAAG,QAAQ,OAAO,SAAS;AAEpD,UAAI,EAAE,KAAK,oBAAoB,sBAAsB;AACnD,aAAK,WAAW,IAAI,oBAAqB,EAAC,KAAK,KAAK,QAAQ;AAAA,MAC7D;AAED,WAAK,SAAS,QAAQ,gBAAgB;AAAA,IAC5C,OAAW;AAEL,eAAS,KAAK,cAAc,kBAAkB,OAAO,CAAC;AAEtD,WAAK,SAAS,aAAa,MAAM,IAAI,gBAAgB,OAAO,WAAW,CAAC,CAAC;AACzE,WAAK,SAAS,WAAW,GAAG,WAAW;AACvC,WAAK,SAAS,WAAW,GAAG,cAAc;AAC1C,WAAK,SAAS,WAAW,GAAG,QAAQ,OAAO,UAAU,SAAS;AAE9D,UAAI,EAAE,KAAK,oBAAoB,sBAAsB;AACnD,aAAK,WAAW,IAAI,oBAAqB,EAAC,KAAK,KAAK,QAAQ;AAAA,MAC7D;AAED,WAAK,SAAS,QAAQ,gBAAgB;AAEtC,WAAK,SAAS,SAAS,cAAc,QAAQ,OAAO;AACpD,WAAK,SAAS,SAAS,cAAc,cAAc;AAAA,IACpD;AAAA,EACF;AAAA,EAED,eAAe;AAAA,IACb,eAAe,SAAU,GAAG,GAAG,GAAG,OAAO;AACvC,UAAI,SAAS,GAAG;AACd,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;AAC3C,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;AAC3C,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG;AAC3C,eAAO,IAAI,WAAW,CAAC,MAAM,MAAM,IAAI,CAAC;AAAA,MAChD,WAAiB,SAAS,GAAG;AACrB,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK;AAC7C,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK;AAC7C,cAAM,OAAO,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK;AAC7C,eAAO,IAAI,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC;AAAA,MACjD,OAAa;AACL,gBAAQ,MAAM,gCAAgC;AAAA,MAC/C;AAAA,IACF;AAAA,IAED,eAAe,SAAU,OAAO,OAAO;AACrC,UAAI,SAAS,GAAG;AACd,eAAO,CAAE,MAAM,CAAC,IAAI,MAAO,IAAM,GAAM,MAAM,CAAC,IAAI,MAAO,IAAM,GAAM,MAAM,CAAC,IAAI,MAAO,IAAM,CAAG;AAAA,MACxG,WAAiB,SAAS,GAAG;AACrB,eAAO,CAAE,MAAM,CAAC,IAAI,QAAS,IAAM,GAAM,MAAM,CAAC,IAAI,QAAS,IAAM,GAAM,MAAM,CAAC,IAAI,QAAS,IAAM,CAAG;AAAA,MAC9G,OAAa;AACL,gBAAQ,MAAM,gCAAgC;AAAA,MAC/C;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,SAAU,GAAG,GAAG,GAAG;AAC/B,YAAM,UAAU,SAAS,OAAO,IAAM,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK;AACzE,YAAM,UAAU,SAAS,OAAO,IAAM,KAAK,KAAK;AAChD,aAAO,IAAI,YAAY,CAAC,SAAS,OAAO,CAAC;AAAA,IAC1C;AAAA;AAAA,IAGD,eAAe,SAAU,GAAG,GAAG,GAAG,OAAO;AACvC,UAAI,KAAK,KAAK,MAAM,YAAY;AAIhC,aAAO,MAAM,cAAc,GAAG,GAAG,GAAG,SAAS,OAAO;AACpD,YAAM,cAAc,GAAG;AACvB,gBAAU,IAAI,GAAG,GAAG,GAAG,GAAG;AAE1B,YAAM,cAAc,GAAG,GAAG,GAAG,QAAQ,OAAO;AAC5C,YAAM,cAAc,GAAG;AACvB,mBAAa,IAAI,GAAG,GAAG,GAAG,GAAG;AAE7B,UAAI,aAAa,SAAS;AACxB,eAAO;AACP,kBAAU;AAAA,MACX;AAED,YAAM,cAAc,GAAG,GAAG,GAAG,SAAS,MAAM;AAC5C,YAAM,cAAc,GAAG;AACvB,mBAAa,IAAI,GAAG,GAAG,GAAG,GAAG;AAE7B,UAAI,aAAa,SAAS;AACxB,eAAO;AACP,kBAAU;AAAA,MACX;AAED,YAAM,cAAc,GAAG,GAAG,GAAG,QAAQ,MAAM;AAC3C,YAAM,cAAc,GAAG;AACvB,mBAAa,IAAI,GAAG,GAAG,GAAG,GAAG;AAE7B,UAAI,aAAa,SAAS;AACxB,eAAO;AAAA,MACR;AAED,aAAO;AAEP,eAAS,cAAc,IAAI,IAAI,IAAI,OAAO,OAAO;AAC/C,YAAIA,KAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACvD,YAAIC,KAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAEvD,YAAI,IAAI,GAAG;AACT,cAAI,SAAS,IAAI,KAAK,IAAIA,EAAC,MAAMD,MAAK,IAAI,IAAI;AAC9C,cAAI,SAAS,IAAI,KAAK,IAAIA,EAAC,MAAMC,MAAK,IAAI,IAAI;AAE9C,UAAAD,KAAI;AACJ,UAAAC,KAAI;AAEJ,cAAI,OAAO,IAAI,KAAK,IAAID,EAAC,IAAI,KAAK,IAAIC,EAAC;AACvC,cAAI,OAAO,GAAG;AACZ,oBAAQ;AACR,YAAAD,MAAKA,KAAI,IAAI,OAAO,IAAI,CAAC,OAAO;AAChC,YAAAC,MAAKA,KAAI,IAAI,OAAO,IAAI,CAAC,OAAO;AAAA,UACjC;AAAA,QACF;AAED,YAAI,SAAS,GAAG;AACd,iBAAO,IAAI,UAAU,CAAC,KAAK,KAAK,EAAED,KAAI,SAASA,KAAI,IAAI,IAAI,EAAE,GAAG,KAAK,KAAK,EAAEC,KAAI,SAASA,KAAI,IAAI,IAAI,EAAE,CAAC,CAAC;AAAA,QAC1G;AAED,YAAI,SAAS,GAAG;AACd,iBAAO,IAAI,WAAW;AAAA,YACpB,KAAK,KAAK,EAAED,KAAI,WAAWA,KAAI,IAAI,IAAI,EAAE;AAAA,YACzC,KAAK,KAAK,EAAEC,KAAI,WAAWA,KAAI,IAAI,IAAI,EAAE;AAAA,UACrD,CAAW;AAAA,QACF;AAAA,MACF;AAED,eAAS,cAAcC,MAAK;AAC1B,YAAIF,KAAIE,KAAI,CAAC;AACb,YAAID,KAAIC,KAAI,CAAC;AAEb,YAAI,SAAS,GAAG;AACd,UAAAF,MAAKA,KAAI,IAAI,MAAM;AACnB,UAAAC,MAAKA,KAAI,IAAI,MAAM;AAAA,QAC7B,WAAmB,SAAS,GAAG;AACrB,UAAAD,MAAKA,KAAI,IAAI,QAAQ;AACrB,UAAAC,MAAKA,KAAI,IAAI,QAAQ;AAAA,QACtB;AAED,YAAIE,KAAI,IAAI,KAAK,IAAIH,EAAC,IAAI,KAAK,IAAIC,EAAC;AAEpC,YAAIE,KAAI,GAAG;AACT,cAAI,OAAOH;AACX,UAAAA,MAAK,IAAI,KAAK,IAAIC,EAAC,MAAMD,MAAK,IAAI,IAAI;AACtC,UAAAC,MAAK,IAAI,KAAK,IAAI,IAAI,MAAMA,MAAK,IAAI,IAAI;AAAA,QAC1C;AAED,YAAI,SAAS,KAAK,KAAKD,KAAIA,KAAIC,KAAIA,KAAIE,KAAIA,EAAC;AAE5C,eAAO,CAACH,KAAI,QAAQC,KAAI,QAAQE,KAAI,MAAM;AAAA,MAC3C;AAED,eAAS,IAAIH,IAAGC,IAAGE,IAAG,MAAM;AAC1B,eAAOH,KAAI,KAAK,CAAC,IAAIC,KAAI,KAAK,CAAC,IAAIE,KAAI,KAAK,CAAC;AAAA,MAC9C;AAAA,IACF;AAAA,IAED,iBAAiB,SAAU,OAAO,OAAO;AACvC,UAAI,WAAW;AAEf,UAAI,SAAS,GAAG;AACd,oBAAY,IAAI,WAAW,MAAM,MAAM;AACvC,mBAAW;AAAA,MACnB,WAAiB,SAAS,GAAG;AACrB,oBAAY,IAAI,YAAY,MAAM,MAAM;AACxC,mBAAW;AAAA,MACnB,OAAa;AACL,gBAAQ,MAAM,yBAAyB;AAAA,MACxC;AAED,YAAM,YAAY,IAAI,QAAS;AAE/B,YAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,YAAM,MAAM,IAAI,aAAa,CAAC;AAE9B,UAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO;AAClC,UAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO;AAEnC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,MACvC;AAED,gBAAU;AAAA,QACR,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,QAAQ;AAAA,MACrG;AAED,gBAAU,SAAS,EAAE,IAAI,IAAI,CAAC;AAC9B,gBAAU,SAAS,EAAE,IAAI,IAAI,CAAC;AAC9B,gBAAU,SAAS,EAAE,IAAI,IAAI,CAAC;AAE9B,gBAAU,UAAW;AAErB,YAAM,aAAa,IAAI,aAAa;AAAA,QAClC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAA,QACnD,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAA,QACnD,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAA,MAC3D,CAAO;AAED,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,kBAAU,IAAI,CAAC,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AACrE,kBAAU,IAAI,CAAC,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AACrE,kBAAU,IAAI,CAAC,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AAAA,MACtE;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,IAED,mBAAmB,SAAU,OAAO,OAAO;AACzC,UAAI,WAAW;AAEf,UAAI,SAAS,GAAG;AACd,oBAAY,IAAI,WAAW,MAAM,MAAM;AACvC,mBAAW;AAAA,MACnB,WAAiB,SAAS,GAAG;AACrB,oBAAY,IAAI,YAAY,MAAM,MAAM;AACxC,mBAAW;AAAA,MACnB,OAAa;AACL,gBAAQ,MAAM,yBAAyB;AAAA,MACxC;AAED,YAAM,YAAY,IAAI,QAAS;AAE/B,YAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,YAAM,MAAM,IAAI,aAAa,CAAC;AAE9B,UAAI,CAAC,IAAI,IAAI,CAAC,IAAI,OAAO;AACzB,UAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO;AAE1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AACtC,YAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,MACvC;AAED,gBAAU,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,QAAQ;AAE1E,gBAAU,SAAS,CAAC,IAAI,IAAI,CAAC;AAC7B,gBAAU,SAAS,CAAC,IAAI,IAAI,CAAC;AAE7B,gBAAU,UAAW;AAErB,YAAM,aAAa,IAAI,aAAa;AAAA,QAClC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAA,QACnD,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAA,MAC3D,CAAO;AAED,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,kBAAU,IAAI,CAAC,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AACrE,kBAAU,IAAI,CAAC,IAAI,KAAK,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC;AAAA,MACtE;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,EACF;AACH;AAOA,MAAM,4BAA4B,kBAAkB;AAAA,EAClD,YAAY,YAAY;AACtB,UAAO;AAEP,SAAK,UAAU,CAAE;AACjB,SAAK,OAAO;AACZ,SAAK,WAAW,cAAc,MAAM;AAAA,MAClC,UAAU,MAAM;AAAA,MAEhB;AAAA,QACE,gBAAgB,EAAE,OAAO,KAAM;AAAA,QAC/B,eAAe,EAAE,OAAO,KAAM;AAAA,MAC/B;AAAA,IACP,CAAK;AAED,SAAK,eAAe;AAAA,MAClB;AAAA,MAEA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiCA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA;AAAA,MAEA,YAAY;AAAA,MAEZ;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA,MACA;AAAA,MACA;AAAA,MAEA,YAAY;AAAA,MAEZ;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA,MAEA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA,IACN,EAAM,KAAK,IAAI;AAGX,SAAK,iBAAiB;AAAA,MACpB;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MAEZ;AAAA,MAEA,YAAY;AAAA,MAEZ;AAAA,MACA;AAAA,MACA;AAAA,MAEA,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAGZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAGZ,YAAY;AAAA,MAEZ;AAAA,MAEA,YAAY;AAAA,MAEZ;AAAA,MAEA,YAAY;AAAA,MACZ,WAAW,MAAM,YAAY,sBAAsB,YAAY;AAAA,MAC/D,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ;AAAA,IACN,EAAM,KAAK,IAAI;AAEX,SAAK,UAAU,UAAU;AAAA,EAC1B;AACH;"}