{"version": 3, "file": "XRHandModelFactory.cjs", "sources": ["../../src/webxr/XRHandModelFactory.js"], "sourcesContent": ["import { Object3D } from 'three'\nimport { XRHandPrimitiveModel } from './XRHandPrimitiveModel'\nimport { XRHandMeshModel } from './XRHandMeshModel'\n\nclass XRHandModel extends Object3D {\n  constructor(controller) {\n    super()\n\n    this.controller = controller\n    this.motionController = null\n    this.envMap = null\n\n    this.mesh = null\n  }\n\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force)\n\n    if (this.motionController) {\n      this.motionController.updateMesh()\n    }\n  }\n}\n\nclass XRHandModelFactory {\n  constructor() {\n    this.path = null\n  }\n\n  setPath(path) {\n    this.path = path\n\n    return this\n  }\n\n  createHandModel(controller, profile) {\n    const handModel = new XRHandModel(controller)\n\n    controller.addEventListener('connected', (event) => {\n      const xrInputSource = event.data\n\n      if (xrInputSource.hand && !handModel.motionController) {\n        handModel.xrInputSource = xrInputSource\n\n        // @todo Detect profile if not provided\n        if (profile === undefined || profile === 'spheres') {\n          handModel.motionController = new XRHandPrimitiveModel(\n            handModel,\n            controller,\n            this.path,\n            xrInputSource.handedness,\n            { primitive: 'sphere' },\n          )\n        } else if (profile === 'boxes') {\n          handModel.motionController = new XRHandPrimitiveModel(\n            handModel,\n            controller,\n            this.path,\n            xrInputSource.handedness,\n            { primitive: 'box' },\n          )\n        } else if (profile === 'mesh') {\n          handModel.motionController = new XRHandMeshModel(handModel, controller, this.path, xrInputSource.handedness)\n        }\n      }\n    })\n\n    controller.addEventListener('disconnected', () => {\n      // handModel.motionController = null;\n      // handModel.remove( scene );\n      // scene = null;\n    })\n\n    return handModel\n  }\n}\n\nexport { XRHandModelFactory }\n"], "names": ["Object3D", "XRHandPrimitiveModel", "XRHandMeshModel"], "mappings": ";;;;;AAIA,MAAM,oBAAoBA,MAAAA,SAAS;AAAA,EACjC,YAAY,YAAY;AACtB,UAAO;AAEP,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,SAAS;AAEd,SAAK,OAAO;AAAA,EACb;AAAA,EAED,kBAAkB,OAAO;AACvB,UAAM,kBAAkB,KAAK;AAE7B,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,WAAY;AAAA,IACnC;AAAA,EACF;AACH;AAEA,MAAM,mBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,OAAO;AAAA,EACb;AAAA,EAED,QAAQ,MAAM;AACZ,SAAK,OAAO;AAEZ,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,YAAY,SAAS;AACnC,UAAM,YAAY,IAAI,YAAY,UAAU;AAE5C,eAAW,iBAAiB,aAAa,CAAC,UAAU;AAClD,YAAM,gBAAgB,MAAM;AAE5B,UAAI,cAAc,QAAQ,CAAC,UAAU,kBAAkB;AACrD,kBAAU,gBAAgB;AAG1B,YAAI,YAAY,UAAa,YAAY,WAAW;AAClD,oBAAU,mBAAmB,IAAIC,qBAAoB;AAAA,YACnD;AAAA,YACA;AAAA,YACA,KAAK;AAAA,YACL,cAAc;AAAA,YACd,EAAE,WAAW,SAAU;AAAA,UACxB;AAAA,QACX,WAAmB,YAAY,SAAS;AAC9B,oBAAU,mBAAmB,IAAIA,qBAAoB;AAAA,YACnD;AAAA,YACA;AAAA,YACA,KAAK;AAAA,YACL,cAAc;AAAA,YACd,EAAE,WAAW,MAAO;AAAA,UACrB;AAAA,QACX,WAAmB,YAAY,QAAQ;AAC7B,oBAAU,mBAAmB,IAAIC,gBAAAA,gBAAgB,WAAW,YAAY,KAAK,MAAM,cAAc,UAAU;AAAA,QAC5G;AAAA,MACF;AAAA,IACP,CAAK;AAED,eAAW,iBAAiB,gBAAgB,MAAM;AAAA,IAItD,CAAK;AAED,WAAO;AAAA,EACR;AACH;;"}