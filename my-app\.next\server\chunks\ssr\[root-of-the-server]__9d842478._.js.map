{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\nconst DURATION = 0.25;\nconst STAGGER = 0.025;\n\ninterface FlipLinkProps {\n  children: string;\n  href: string;\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  );\n};\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,8OAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;uCAEe", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/image-ripple.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useEffect, useRef, useState } from \"react\"\nimport { OrthographicCamera, useFBO, useTexture } from \"@react-three/drei\"\nimport { Canvas, useFrame, useThree } from \"@react-three/fiber\"\nimport * as THREE from \"three\"\n\nexport default function Scene() {\n  const device = useDimension()\n\n  if (!device.width || !device.height) {\n    return null\n  }\n\n  const frustumSize = device.height\n  const aspect = device.width / device.height\n\n  return (\n    <div className=\"relative flex h-screen w-full items-center justify-center \">\n      <Canvas>\n        <OrthographicCamera\n          makeDefault\n          args={[\n            (frustumSize * aspect) / -2,\n            (frustumSize * aspect) / 2,\n            frustumSize / 2,\n            frustumSize / -2,\n            -1000,\n            1000,\n          ]}\n          position={[0, 0, 2]}\n        />\n        <Model />\n      </Canvas>\n    </div>\n  )\n}\n\nfunction Model() {\n  const { viewport } = useThree()\n  const texture = useTexture(\"/brush.png\")\n  const meshRefs = useRef<(THREE.Mesh | null)[]>([])\n  const [meshes, setMeshes] = useState<JSX.Element[]>([])\n  const mouse = useMouse()\n  const device = useDimension()\n  const [prevMouse, setPrevMouse] = useState({ x: 0, y: 0 })\n  const [currentWave, setCurrentWave] = useState(0)\n  const { gl, camera } = useThree()\n\n  const scene = new THREE.Scene()\n  const max = 100\n\n  const uniforms = useRef({\n    uDisplacement: { value: new THREE.Texture() },\n    uTexture: { value: new THREE.Texture() },\n    winResolution: {\n      value: new THREE.Vector2(0, 0),\n    },\n  })\n\n  const fboBase = useFBO(device.width, device.height)\n  const fboTexture = useFBO(device.width, device.height)\n\n  const { scene: imageScene, camera: imageCamera } = Images(\n    new THREE.Vector2(viewport.width, viewport.height)\n  )\n\n  useEffect(() => {\n    const generatedMeshes = Array.from({ length: max }).map((_, i) => (\n      <mesh\n        key={i}\n        position={[0, 0, 0]}\n        ref={(el) => {\n          meshRefs.current[i] = el\n        }}\n        rotation={[0, 0, Math.random()]}\n        visible={false}\n      >\n        <planeGeometry args={[60, 60, 1, 1]} />\n        <meshBasicMaterial transparent={true} map={texture} />\n      </mesh>\n    ))\n    setMeshes(generatedMeshes)\n  }, [texture])\n\n  function setNewWave(x: number, y: number, currentWave: number) {\n    const mesh = meshRefs.current[currentWave]\n    if (mesh) {\n      mesh.position.x = x\n      mesh.position.y = y\n      mesh.visible = true\n      ;(mesh.material as THREE.Material).opacity = 1\n      mesh.scale.x = 1.75\n      mesh.scale.y = 1.75\n    }\n  }\n\n  function trackMousePos(x: number, y: number) {\n    if (Math.abs(x - prevMouse.x) > 0.1 || Math.abs(y - prevMouse.y) > 0.1) {\n      setCurrentWave((currentWave + 1) % max)\n      setNewWave(x, y, currentWave)\n    }\n    setPrevMouse({ x: x, y: y })\n  }\n\n  useFrame(({ gl, scene: finalScene }) => {\n    const x = mouse.x - device.width / 1.65\n    const y = -mouse.y + device.height / 1.5\n    trackMousePos(x, y)\n    meshRefs.current.forEach((mesh) => {\n      if (mesh && mesh.visible) {\n        mesh.rotation.z += 0.025\n        ;(mesh.material as THREE.MeshBasicMaterial).opacity *= 0.95\n        mesh.scale.x = 0.98 * mesh.scale.x + 0.155\n        mesh.scale.y = 0.98 * mesh.scale.y + 0.155\n      }\n    })\n\n    if (device.width > 0 && device.height > 0) {\n      // uniforms.current.uTexture.value = imageTexture;\n\n      // Render to base texture with meshes\n      gl.setRenderTarget(fboBase)\n      gl.clear()\n      meshRefs.current.forEach((mesh) => {\n        if (mesh && mesh.visible) {\n          scene.add(mesh)\n        }\n      })\n      gl.render(scene, camera)\n      meshRefs.current.forEach((mesh) => {\n        if (mesh && mesh.visible) {\n          scene.remove(mesh)\n        }\n      })\n      uniforms.current.uTexture.value = fboTexture.texture\n\n      gl.setRenderTarget(fboTexture)\n      gl.render(imageScene, imageCamera)\n      uniforms.current.uDisplacement.value = fboBase.texture\n\n      gl.setRenderTarget(null)\n      gl.render(finalScene, camera)\n\n      // Render the scene with updated displacement\n      // gl.setRenderTarget(fboTexture);\n      // gl.clear();\n      // gl.render(scene, camera);\n      // uniforms.current.uTexture.value = fboTexture.texture;\n      // gl.setRenderTarget(null);\n\n      uniforms.current.winResolution.value = new THREE.Vector2(\n        device.width,\n        device.height\n      ).multiplyScalar(device.pixelRatio)\n    }\n  }, 1)\n\n  function Images(viewport: THREE.Vector2) {\n    const scene = new THREE.Scene()\n    const camera = new THREE.OrthographicCamera(\n      viewport.width / -2,\n      viewport.width / 2,\n      viewport.height / 2,\n      viewport.height / -2,\n      -1000,\n      1000\n    )\n    camera.position.z = 2\n    scene.add(camera)\n    const geometry = new THREE.PlaneGeometry(1, 1)\n    const group = new THREE.Group()\n\n    const texture1 = useTexture(\"/picture9.jpeg\")\n    const material1 = new THREE.MeshBasicMaterial({ map: texture1 })\n    const image1 = new THREE.Mesh(geometry, material1)\n    image1.position.x = -0.3 * viewport.width\n    image1.position.y = 0\n    image1.position.z = 1\n    image1.scale.x = 1080 / 4\n    image1.scale.y = 1920 / 4\n    group.add(image1)\n\n    const texture2 = useTexture(\"/picture1.jpeg\")\n    const material2 = new THREE.MeshBasicMaterial({ map: texture2 })\n    const image2 = new THREE.Mesh(geometry, material2)\n    image2.position.x = -0.001 * viewport.width\n    image2.position.y = 0\n    image2.position.z = 1\n    image2.scale.x = 1080 / 4\n    image2.scale.y = 1920 / 4\n    group.add(image2)\n\n    // const texture3 = useTexture('/picture3.jpeg');\n    // const material3 = new THREE.MeshBasicMaterial({ map: texture3 });\n    // const image3 = new THREE.Mesh(geometry, material3);\n    // image3.position.x = 0.25 * viewport.width;\n    // image3.position.y = 0;\n    // image3.position.z = 1;\n    // image3.scale.x = viewport.width / 5;\n    // image3.scale.y = viewport.width / 4;\n    // group.add(image3);\n\n    scene.add(group)\n    return { scene, camera }\n  }\n\n  return (\n    <group>\n      {meshes}\n      {/* <Images /> */}\n      <mesh>\n        <planeGeometry args={[device.width, device.height, 1, 1]} />\n        <shaderMaterial\n          // args={[device.width, device.height, 1]}\n          vertexShader={vertex}\n          fragmentShader={fragment}\n          transparent={true}\n          uniforms={uniforms.current}\n        ></shaderMaterial>\n      </mesh>\n    </group>\n  )\n}\n\nfunction useMouse() {\n  const [mouse, setMouse] = React.useState({ x: 0, y: 0, pixelRatio: 0 })\n\n  const mouseMove = (e: { clientX: any; clientY: any }) => {\n    const { clientX, clientY } = e\n    setMouse({\n      x: clientX,\n      y: clientY,\n      pixelRatio: Math.min(window.devicePixelRatio, 2),\n    })\n  }\n\n  React.useEffect(() => {\n    window.addEventListener(\"mousemove\", mouseMove)\n    return () => {\n      window.removeEventListener(\"mousemove\", mouseMove)\n    }\n  }, [])\n\n  return mouse\n}\n\nfunction useDimension() {\n  const [dimension, setDimension] = React.useState({\n    width: 0,\n    height: 0,\n    pixelRatio: 1,\n  })\n\n  React.useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      const resize = () => {\n        setDimension({\n          width: window.innerWidth,\n          height: window.innerHeight,\n          pixelRatio: window.devicePixelRatio,\n        })\n      }\n\n      resize()\n\n      window.addEventListener(\"resize\", resize)\n\n      return () => window.removeEventListener(\"resize\", resize)\n    }\n  }, [])\n\n  return dimension\n}\n\nconst fragment = `\nuniform sampler2D uTexture;\nuniform sampler2D uDisplacement;\nuniform vec4 winResolution;\nvarying vec2 vUv;\nfloat PI = 3.141592653589793238;\n\nvoid main() {\n  vec2 vUvScreen = gl_FragCoord.xy / winResolution.xy;\n\n  vec4 displacement = texture2D(uDisplacement, vUvScreen);\n  float theta = displacement.r*2.0*PI;\n\n  vec2 dir = vec2(sin(theta),cos(theta));\n  vec2 uv = vUvScreen + dir*displacement.r*0.075;\n  vec4 color = texture2D(uTexture,uv);\n\n  gl_FragColor = color;\n}\n`\n\nconst vertex = `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM,EAAE;QACnC,OAAO;IACT;IAEA,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,SAAS,OAAO,KAAK,GAAG,OAAO,MAAM;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;;8BACL,8OAAC,sKAAA,CAAA,qBAAkB;oBACjB,WAAW;oBACX,MAAM;wBACH,cAAc,SAAU,CAAC;wBACzB,cAAc,SAAU;wBACzB,cAAc;wBACd,cAAc,CAAC;wBACf,CAAC;wBACD;qBACD;oBACD,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;;;;;;8BAErB,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS;IACP,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE9B,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAW;IAC7B,MAAM,MAAM;IAEZ,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACtB,eAAe;YAAE,OAAO,IAAI,+IAAA,CAAA,UAAa;QAAG;QAC5C,UAAU;YAAE,OAAO,IAAI,+IAAA,CAAA,UAAa;QAAG;QACvC,eAAe;YACb,OAAO,IAAI,+IAAA,CAAA,UAAa,CAAC,GAAG;QAC9B;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM;IAClD,MAAM,aAAa,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM;IAErD,MAAM,EAAE,OAAO,UAAU,EAAE,QAAQ,WAAW,EAAE,GAAG,OACjD,IAAI,+IAAA,CAAA,UAAa,CAAC,SAAS,KAAK,EAAE,SAAS,MAAM;IAGnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAI,GAAG,GAAG,CAAC,CAAC,GAAG,kBAC1D,8OAAC;gBAEC,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,KAAK,CAAC;oBACJ,SAAS,OAAO,CAAC,EAAE,GAAG;gBACxB;gBACA,UAAU;oBAAC;oBAAG;oBAAG,KAAK,MAAM;iBAAG;gBAC/B,SAAS;;kCAET,8OAAC;wBAAc,MAAM;4BAAC;4BAAI;4BAAI;4BAAG;yBAAE;;;;;;kCACnC,8OAAC;wBAAkB,aAAa;wBAAM,KAAK;;;;;;;eATtC;;;;;QAYT,UAAU;IACZ,GAAG;QAAC;KAAQ;IAEZ,SAAS,WAAW,CAAS,EAAE,CAAS,EAAE,WAAmB;QAC3D,MAAM,OAAO,SAAS,OAAO,CAAC,YAAY;QAC1C,IAAI,MAAM;YACR,KAAK,QAAQ,CAAC,CAAC,GAAG;YAClB,KAAK,QAAQ,CAAC,CAAC,GAAG;YAClB,KAAK,OAAO,GAAG;YACb,KAAK,QAAQ,CAAoB,OAAO,GAAG;YAC7C,KAAK,KAAK,CAAC,CAAC,GAAG;YACf,KAAK,KAAK,CAAC,CAAC,GAAG;QACjB;IACF;IAEA,SAAS,cAAc,CAAS,EAAE,CAAS;QACzC,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK;YACtE,eAAe,CAAC,cAAc,CAAC,IAAI;YACnC,WAAW,GAAG,GAAG;QACnB;QACA,aAAa;YAAE,GAAG;YAAG,GAAG;QAAE;IAC5B;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,EAAE,EAAE,EAAE,OAAO,UAAU,EAAE;QACjC,MAAM,IAAI,MAAM,CAAC,GAAG,OAAO,KAAK,GAAG;QACnC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,MAAM,GAAG;QACrC,cAAc,GAAG;QACjB,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;gBACxB,KAAK,QAAQ,CAAC,CAAC,IAAI;gBACjB,KAAK,QAAQ,CAA6B,OAAO,IAAI;gBACvD,KAAK,KAAK,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;gBACrC,KAAK,KAAK,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;YACvC;QACF;QAEA,IAAI,OAAO,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;YACzC,kDAAkD;YAElD,qCAAqC;YACrC,GAAG,eAAe,CAAC;YACnB,GAAG,KAAK;YACR,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;oBACxB,MAAM,GAAG,CAAC;gBACZ;YACF;YACA,GAAG,MAAM,CAAC,OAAO;YACjB,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;oBACxB,MAAM,MAAM,CAAC;gBACf;YACF;YACA,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,OAAO;YAEpD,GAAG,eAAe,CAAC;YACnB,GAAG,MAAM,CAAC,YAAY;YACtB,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,OAAO;YAEtD,GAAG,eAAe,CAAC;YACnB,GAAG,MAAM,CAAC,YAAY;YAEtB,6CAA6C;YAC7C,kCAAkC;YAClC,cAAc;YACd,4BAA4B;YAC5B,wDAAwD;YACxD,4BAA4B;YAE5B,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,+IAAA,CAAA,UAAa,CACtD,OAAO,KAAK,EACZ,OAAO,MAAM,EACb,cAAc,CAAC,OAAO,UAAU;QACpC;IACF,GAAG;IAEH,SAAS,OAAO,QAAuB;QACrC,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAW;QAC7B,MAAM,SAAS,IAAI,+IAAA,CAAA,qBAAwB,CACzC,SAAS,KAAK,GAAG,CAAC,GAClB,SAAS,KAAK,GAAG,GACjB,SAAS,MAAM,GAAG,GAClB,SAAS,MAAM,GAAG,CAAC,GACnB,CAAC,MACD;QAEF,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,MAAM,GAAG,CAAC;QACV,MAAM,WAAW,IAAI,+IAAA,CAAA,gBAAmB,CAAC,GAAG;QAC5C,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAW;QAE7B,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,MAAM,YAAY,IAAI,+IAAA,CAAA,oBAAuB,CAAC;YAAE,KAAK;QAAS;QAC9D,MAAM,SAAS,IAAI,+IAAA,CAAA,OAAU,CAAC,UAAU;QACxC,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,SAAS,KAAK;QACzC,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,MAAM,GAAG,CAAC;QAEV,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,MAAM,YAAY,IAAI,+IAAA,CAAA,oBAAuB,CAAC;YAAE,KAAK;QAAS;QAC9D,MAAM,SAAS,IAAI,+IAAA,CAAA,OAAU,CAAC,UAAU;QACxC,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,QAAQ,SAAS,KAAK;QAC3C,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,MAAM,GAAG,CAAC;QAEV,iDAAiD;QACjD,oEAAoE;QACpE,sDAAsD;QACtD,6CAA6C;QAC7C,yBAAyB;QACzB,yBAAyB;QACzB,uCAAuC;QACvC,uCAAuC;QACvC,qBAAqB;QAErB,MAAM,GAAG,CAAC;QACV,OAAO;YAAE;YAAO;QAAO;IACzB;IAEA,qBACE,8OAAC;;YACE;0BAED,8OAAC;;kCACC,8OAAC;wBAAc,MAAM;4BAAC,OAAO,KAAK;4BAAE,OAAO,MAAM;4BAAE;4BAAG;yBAAE;;;;;;kCACxD,8OAAC;wBACC,0CAA0C;wBAC1C,cAAc;wBACd,gBAAgB;wBAChB,aAAa;wBACb,UAAU,SAAS,OAAO;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QAAE,GAAG;QAAG,GAAG;QAAG,YAAY;IAAE;IAErE,MAAM,YAAY,CAAC;QACjB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,SAAS;YACP,GAAG;YACH,GAAG;YACH,YAAY,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE;QAChD;IACF;IAEA,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;QAC1C;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAEA,SAAS;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QAC/C,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IAEA,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd;;IAeF,GAAG,EAAE;IAEL,OAAO;AACT;AAEA,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;AAmBlB,CAAC;AAED,MAAM,SAAS,CAAC;;;;;;;AAOhB,CAAC", "debugId": null}}]}