(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/image-ripple.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_three_build_three_core_df0bde7e.js",
  "static/chunks/node_modules_three_build_three_module_230a9f89.js",
  "static/chunks/node_modules_three_build_three_module_90b4155f.js",
  "static/chunks/node_modules_@react-three_fiber_dist_ee5ccdc9._.js",
  "static/chunks/node_modules_dd3e88d2._.js",
  "static/chunks/src_components_ui_image-ripple_tsx_2854ebc1._.js",
  "static/chunks/src_components_ui_image-ripple_tsx_d4ca0bc7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/image-ripple.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);